import{_ as q,j as U,G as j,P as G,I as J,c as u,f as d,B as L,a,w as t,h as o,b as f,C as Q,m as n,t as i,x as T,r as V,J as A,o as K,p as O,q as W,Q as X,E as D,R as Y,e as l}from"./index-BnaD8Tdo.js";import{S as Z,a as $,r as ee}from"./schedule-task-form-CxlekAMP.js";const te={name:"CmdbScheduleTaskDetail",components:{Back:J,Edit:G,VideoPlay:j,RefreshRight:U,ScheduleTaskForm:Z},setup(){const g=X(),w=Y(),H=V(!1),e=V({}),B=V(null),I=V({}),M=V(null),p=A({historyDetail:!1,edit:!1}),y=async()=>{const c=g.query.id;if(!c){D.error("缺少任务ID参数");return}H.value=!0;try{const m=await $({id:c});m.code===0?e.value=m.msg||{}:D.error(m.msg||"获取调度任务详情失败")}catch(m){console.error("获取调度任务详情失败:",m),D.error("获取调度任务详情失败")}finally{H.value=!1}},E=c=>c?new Date(c).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(/\//g,"-"):"-",F=(c,m)=>{if(!c||!m)return"-";const S=new Date(c).getTime(),z=new Date(m).getTime(),k=Math.abs(z-S)/1e3;if(k<60)return`${Math.round(k)}秒`;if(k<3600){const s=Math.floor(k/60),C=Math.round(k%60);return`${s}分${C}秒`}else{const s=Math.floor(k/3600),C=Math.floor(k%3600/60),P=Math.round(k%60);return`${s}小时${C}分${P}秒`}},N=()=>{w.push("/cmdb_schedule_tasks")},r=()=>{y()},_=()=>{I.value={...e.value},p.edit=!0},x=()=>{p.edit=!1,y()},v=async()=>{try{const c=await ee({id:e.value.id});c.code===0?(D.success("任务已开始执行"),setTimeout(()=>{y()},1e3)):D.error(c.msg||"执行失败")}catch(c){console.error("执行调度任务失败:",c),D.error("执行调度任务失败")}},h=c=>{window.open(`#/cmdb_discovery_results?task_id=${c}`,"_blank")},R=c=>{B.value=c,p.historyDetail=!0};return K(()=>{y()}),{loading:H,taskDetail:e,selectedHistory:B,dialogVisible:p,formData:I,scheduleTaskFormRef:M,formatDateTime:E,calculateDuration:F,goBack:N,handleRefresh:r,handleEdit:_,handleFormSubmit:x,handleRun:v,viewDiscoveryTask:h,viewHistoryDetail:R}}},b=g=>(O("data-v-34255b74"),g=g(),W(),g),ae={class:"app-container"},se={class:"page-header"},le={class:"header-title"},oe=b(()=>d("h2",null,"调度任务详情",-1)),ie={class:"header-actions"},de={class:"card-header"},ne=b(()=>d("span",null,"基本信息",-1)),re={class:"detail-info"},ce={key:0},_e={key:1},ue={key:0},fe={key:1},he={key:0},ye={key:1},me={key:0},ke={key:1},pe={class:"description-content"},we={class:"card-header"},be=b(()=>d("span",null,"关联的发现任务",-1)),ge={class:"task-count"},De={class:"card-header"},ve=b(()=>d("span",null,"执行历史",-1)),Ce={class:"history-count"},Te={key:0},Ve={key:1},He={key:0},xe={key:1},Re={key:0},Se={key:1},Be={key:2},Ie={class:"task-stats"},Me={class:"stat-item"},Ee=b(()=>d("span",{class:"stat-label"},"总数:",-1)),Fe={class:"stat-value"},Ne={class:"stat-item"},ze=b(()=>d("span",{class:"stat-label"},"成功:",-1)),Pe={class:"stat-value success"},qe={class:"stat-item"},Ue=b(()=>d("span",{class:"stat-label"},"失败:",-1)),je={class:"stat-value error"},Ge={key:0},Je={class:"stat-value success"},Le={class:"stat-value error"},Qe={key:0,class:"history-result"},Ae=b(()=>d("div",{class:"result-title"},"执行结果:",-1)),Ke={class:"result-content"};function Oe(g,w,H,e,B,I){const M=f("Back"),p=f("el-icon"),y=f("el-button"),E=f("Edit"),F=f("VideoPlay"),N=f("RefreshRight"),r=f("el-tag"),_=f("el-descriptions-item"),x=f("el-descriptions"),v=f("el-card"),h=f("el-table-column"),R=f("el-table"),c=f("el-empty"),m=f("el-scrollbar"),S=f("el-dialog"),z=f("schedule-task-form"),k=Q("loading");return l(),u("div",ae,[d("div",se,[d("div",le,[a(y,{onClick:e.goBack,type:"text",size:"small"},{default:t(()=>[a(p,null,{default:t(()=>[a(M)]),_:1}),o(" 返回列表 ")]),_:1},8,["onClick"]),oe]),d("div",ie,[a(y,{type:"primary",onClick:e.handleEdit},{default:t(()=>[a(p,null,{default:t(()=>[a(E)]),_:1}),o(" 编辑 ")]),_:1},8,["onClick"]),a(y,{type:"success",onClick:e.handleRun},{default:t(()=>[a(p,null,{default:t(()=>[a(F)]),_:1}),o(" 执行 ")]),_:1},8,["onClick"]),a(y,{type:"warning",onClick:e.handleRefresh},{default:t(()=>[a(p,null,{default:t(()=>[a(N)]),_:1}),o(" 刷新 ")]),_:1},8,["onClick"])])]),L((l(),n(v,{class:"detail-card"},{header:t(()=>[d("div",de,[ne,d("div",null,[e.taskDetail.status==="active"?(l(),n(r,{key:0,type:"success"},{default:t(()=>[o("激活")]),_:1})):e.taskDetail.status==="inactive"?(l(),n(r,{key:1,type:"info"},{default:t(()=>[o("停用")]),_:1})):e.taskDetail.status==="paused"?(l(),n(r,{key:2,type:"warning"},{default:t(()=>[o("暂停")]),_:1})):T("",!0)])])]),default:t(()=>[d("div",re,[a(x,{column:2,border:""},{default:t(()=>[a(_,{label:"任务ID"},{default:t(()=>[o(i(e.taskDetail.id||"-"),1)]),_:1}),a(_,{label:"任务名称"},{default:t(()=>[o(i(e.taskDetail.task_name||"-"),1)]),_:1}),a(_,{label:"调度类型"},{default:t(()=>[e.taskDetail.schedule_type==="manual"?(l(),n(r,{key:0,type:"info"},{default:t(()=>[o("手动")]),_:1})):e.taskDetail.schedule_type==="once"?(l(),n(r,{key:1,type:"success"},{default:t(()=>[o("一次性")]),_:1})):e.taskDetail.schedule_type==="daily"?(l(),n(r,{key:2,type:"primary"},{default:t(()=>[o("每日")]),_:1})):e.taskDetail.schedule_type==="weekly"?(l(),n(r,{key:3,type:"warning"},{default:t(()=>[o("每周")]),_:1})):(l(),n(r,{key:4,type:"info"},{default:t(()=>[o(i(e.taskDetail.schedule_type),1)]),_:1}))]),_:1}),a(_,{label:"调度值"},{default:t(()=>[o(i(e.taskDetail.schedule_value||"-"),1)]),_:1}),a(_,{label:"下次执行时间"},{default:t(()=>[e.taskDetail.next_run_time?(l(),u("span",ce,i(e.formatDateTime(e.taskDetail.next_run_time)),1)):(l(),u("span",_e,"-"))]),_:1}),a(_,{label:"上次执行时间"},{default:t(()=>[e.taskDetail.last_run_time?(l(),u("span",ue,i(e.formatDateTime(e.taskDetail.last_run_time)),1)):(l(),u("span",fe,"-"))]),_:1}),a(_,{label:"创建时间"},{default:t(()=>[e.taskDetail.created_at?(l(),u("span",he,i(e.formatDateTime(e.taskDetail.created_at)),1)):(l(),u("span",ye,"-"))]),_:1}),a(_,{label:"创建人"},{default:t(()=>[o(i(e.taskDetail.created_by||"-"),1)]),_:1}),a(_,{label:"更新时间"},{default:t(()=>[e.taskDetail.updated_at?(l(),u("span",me,i(e.formatDateTime(e.taskDetail.updated_at)),1)):(l(),u("span",ke,"-"))]),_:1}),a(_,{label:"更新人"},{default:t(()=>[o(i(e.taskDetail.updated_by||"-"),1)]),_:1}),a(_,{span:2,label:"任务描述"},{default:t(()=>[d("div",pe,i(e.taskDetail.description||"-"),1)]),_:1})]),_:1})])]),_:1})),[[k,e.loading]]),a(v,{class:"detail-card"},{header:t(()=>{var s;return[d("div",we,[be,d("span",ge,"共 "+i(((s=e.taskDetail.items)==null?void 0:s.length)||0)+" 个任务",1)])]}),default:t(()=>[a(R,{data:e.taskDetail.items||[],style:{width:"100%"},border:"","header-cell-style":{background:"#f8f8f9",color:"#606266"}},{default:t(()=>[a(h,{type:"index",label:"序号",width:"80",align:"center"}),a(h,{prop:"discovery_task_id",label:"任务ID",width:"100",align:"center"}),a(h,{prop:"task_name",label:"任务名称","min-width":"200","show-overflow-tooltip":""}),a(h,{prop:"task_type",label:"任务类型",width:"120",align:"center"}),a(h,{label:"状态",width:"100",align:"center"},{default:t(s=>[s.row.discovery_status==="running"?(l(),n(r,{key:0,type:"warning"},{default:t(()=>[o("运行中")]),_:1})):s.row.discovery_status==="completed"?(l(),n(r,{key:1,type:"success"},{default:t(()=>[o("完成")]),_:1})):s.row.discovery_status==="failed"?(l(),n(r,{key:2,type:"danger"},{default:t(()=>[o("失败")]),_:1})):s.row.discovery_status==="stopped"?(l(),n(r,{key:3,type:"info"},{default:t(()=>[o("已停止")]),_:1})):s.row.discovery_status==="queued"?(l(),n(r,{key:4,type:"info"},{default:t(()=>[o("排队中")]),_:1})):(l(),n(r,{key:5,type:"info"},{default:t(()=>[o(i(s.row.discovery_status),1)]),_:2},1024))]),_:1}),a(h,{label:"操作",width:"150",align:"center",fixed:"right"},{default:t(s=>[a(y,{type:"primary",size:"small",onClick:C=>e.viewDiscoveryTask(s.row.discovery_task_id)},{default:t(()=>[o(" 查看 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),!e.taskDetail.items||e.taskDetail.items.length===0?(l(),n(c,{key:0,description:"暂无关联的发现任务"})):T("",!0)]),_:1}),a(v,{class:"detail-card"},{header:t(()=>{var s;return[d("div",De,[ve,d("span",Ce,"共 "+i(((s=e.taskDetail.history)==null?void 0:s.length)||0)+" 条记录",1)])]}),default:t(()=>[a(R,{data:e.taskDetail.history||[],style:{width:"100%"},border:"","header-cell-style":{background:"#f8f8f9",color:"#606266"}},{default:t(()=>[a(h,{prop:"id",label:"历史ID",width:"80",align:"center"}),a(h,{label:"开始时间","min-width":"180","show-overflow-tooltip":""},{default:t(s=>[s.row.start_time?(l(),u("span",Te,i(e.formatDateTime(s.row.start_time)),1)):(l(),u("span",Ve,"-"))]),_:1}),a(h,{label:"结束时间","min-width":"180","show-overflow-tooltip":""},{default:t(s=>[s.row.end_time?(l(),u("span",He,i(e.formatDateTime(s.row.end_time)),1)):(l(),u("span",xe,"-"))]),_:1}),a(h,{label:"执行时间",width:"120",align:"center"},{default:t(s=>[s.row.start_time&&s.row.end_time?(l(),u("span",Re,i(e.calculateDuration(s.row.start_time,s.row.end_time)),1)):s.row.start_time&&!s.row.end_time?(l(),u("span",Se,"运行中")):(l(),u("span",Be,"-"))]),_:1}),a(h,{label:"状态",width:"100",align:"center"},{default:t(s=>[s.row.status==="started"?(l(),n(r,{key:0,type:"warning"},{default:t(()=>[o("运行中")]),_:1})):s.row.status==="completed"?(l(),n(r,{key:1,type:"success"},{default:t(()=>[o("完成")]),_:1})):s.row.status==="failed"?(l(),n(r,{key:2,type:"danger"},{default:t(()=>[o("失败")]),_:1})):(l(),n(r,{key:3,type:"info"},{default:t(()=>[o(i(s.row.status),1)]),_:2},1024))]),_:1}),a(h,{label:"任务统计",width:"200",align:"center"},{default:t(s=>[d("div",Ie,[d("span",Me,[Ee,d("span",Fe,i(s.row.total_tasks||0),1)]),d("span",Ne,[ze,d("span",Pe,i(s.row.completed_tasks||0),1)]),d("span",qe,[Ue,d("span",je,i(s.row.failed_tasks||0),1)])])]),_:1}),a(h,{label:"操作",width:"100",align:"center",fixed:"right"},{default:t(s=>[a(y,{type:"primary",size:"small",onClick:C=>e.viewHistoryDetail(s.row)},{default:t(()=>[o(" 详情 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),!e.taskDetail.history||e.taskDetail.history.length===0?(l(),n(c,{key:0,description:"暂无执行历史"})):T("",!0)]),_:1}),a(S,{modelValue:e.dialogVisible.historyDetail,"onUpdate:modelValue":w[0]||(w[0]=s=>e.dialogVisible.historyDetail=s),title:"执行历史详情",width:"800px"},{default:t(()=>[e.selectedHistory?(l(),u("div",Ge,[a(x,{column:2,border:""},{default:t(()=>[a(_,{label:"历史ID"},{default:t(()=>[o(i(e.selectedHistory.id),1)]),_:1}),a(_,{label:"状态"},{default:t(()=>[e.selectedHistory.status==="started"?(l(),n(r,{key:0,type:"warning"},{default:t(()=>[o("运行中")]),_:1})):e.selectedHistory.status==="completed"?(l(),n(r,{key:1,type:"success"},{default:t(()=>[o("完成")]),_:1})):e.selectedHistory.status==="failed"?(l(),n(r,{key:2,type:"danger"},{default:t(()=>[o("失败")]),_:1})):(l(),n(r,{key:3,type:"info"},{default:t(()=>[o(i(e.selectedHistory.status),1)]),_:1}))]),_:1}),a(_,{label:"开始时间"},{default:t(()=>[o(i(e.formatDateTime(e.selectedHistory.start_time)),1)]),_:1}),a(_,{label:"结束时间"},{default:t(()=>[o(i(e.selectedHistory.end_time?e.formatDateTime(e.selectedHistory.end_time):"运行中"),1)]),_:1}),a(_,{label:"总任务数"},{default:t(()=>[o(i(e.selectedHistory.total_tasks||0),1)]),_:1}),a(_,{label:"成功/失败"},{default:t(()=>[d("span",Je,i(e.selectedHistory.completed_tasks||0),1),o(" / "),d("span",Le,i(e.selectedHistory.failed_tasks||0),1)]),_:1})]),_:1}),e.selectedHistory.result?(l(),u("div",Qe,[Ae,a(m,{height:"300px"},{default:t(()=>[d("pre",Ke,i(e.selectedHistory.result),1)]),_:1})])):T("",!0)])):T("",!0)]),_:1},8,["modelValue"]),a(S,{modelValue:e.dialogVisible.edit,"onUpdate:modelValue":w[2]||(w[2]=s=>e.dialogVisible.edit=s),title:"编辑调度任务",width:"800px","destroy-on-close":""},{default:t(()=>[a(z,{ref:"scheduleTaskFormRef","form-data":e.formData,mode:"edit",onSubmit:e.handleFormSubmit,onCancel:w[1]||(w[1]=s=>e.dialogVisible.edit=!1)},null,8,["form-data","onSubmit"])]),_:1},8,["modelValue"])])}const Ye=q(te,[["render",Oe],["__scopeId","data-v-34255b74"]]);export{Ye as default};
