<template>
  <div class="debug-container">
    <h2>API调试测试页面</h2>
    
    <div class="test-section">
      <h3>测试数据字典API</h3>
      <el-button @click="testDataDictionary" type="primary">测试数据字典API</el-button>
      <el-button @click="testMethodEndpoint" type="success">测试方法检测API</el-button>
    </div>
    
    <div class="result-section">
      <h3>测试结果</h3>
      <pre>{{ testResult }}</pre>
    </div>
    
    <div class="log-section">
      <h3>控制台日志</h3>
      <div class="log-content">
        <div v-for="(log, index) in logs" :key="index" :class="log.type">
          {{ log.timestamp }} - {{ log.message }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DebugApiTest',
  data() {
    return {
      testResult: '',
      logs: []
    }
  },
  methods: {
    addLog(message, type = 'info') {
      this.logs.unshift({
        timestamp: new Date().toLocaleTimeString(),
        message: message,
        type: type
      });
      // 只保留最近50条日志
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50);
      }
    },
    
    async testDataDictionary() {
      this.addLog('开始测试数据字典API...', 'info');
      
      try {
        const response = await this.$axios.post('/api/get_cmdb_data_dictionary', {
          dict_code: 'B'
        });
        
        this.testResult = JSON.stringify(response.data, null, 2);
        this.addLog('数据字典API测试成功', 'success');
      } catch (error) {
        this.testResult = JSON.stringify({
          error: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        }, null, 2);
        this.addLog(`数据字典API测试失败: ${error.message}`, 'error');
      }
    },
    
    async testMethodEndpoint() {
      this.addLog('开始测试方法检测API...', 'info');
      
      try {
        const response = await this.$axios.post('/api/test_method', {
          test: 'data'
        });
        
        this.testResult = JSON.stringify(response.data, null, 2);
        this.addLog('方法检测API测试成功', 'success');
      } catch (error) {
        this.testResult = JSON.stringify({
          error: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        }, null, 2);
        this.addLog(`方法检测API测试失败: ${error.message}`, 'error');
      }
    }
  }
}
</script>

<style scoped>
.debug-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.result-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.result-section pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 3px;
  overflow-x: auto;
  max-height: 400px;
}

.log-section {
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.log-content {
  background: #f9f9f9;
  padding: 10px;
  border-radius: 3px;
  max-height: 300px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 12px;
}

.log-content .info {
  color: #333;
}

.log-content .success {
  color: #67c23a;
}

.log-content .error {
  color: #f56c6c;
}

.log-content .warning {
  color: #e6a23c;
}
</style>
