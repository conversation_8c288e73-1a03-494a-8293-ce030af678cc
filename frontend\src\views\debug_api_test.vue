<template>
  <div class="debug-container">
    <h2>API调试测试页面</h2>
    
    <div class="test-section">
      <h3>测试数据字典API</h3>
      <el-button @click="testDataDictionary" type="primary">测试数据字典API (POST)</el-button>
      <el-button @click="testDataDictionaryGET" type="warning">测试数据字典API (GET)</el-button>
      <el-button @click="testMethodEndpoint" type="success">测试方法检测API</el-button>
      <el-button @click="testWithDifferentHeaders" type="info">测试不同请求头</el-button>
    </div>
    
    <div class="result-section">
      <h3>测试结果</h3>
      <pre>{{ testResult }}</pre>
    </div>
    
    <div class="log-section">
      <h3>控制台日志</h3>
      <div class="log-content">
        <div v-for="(log, index) in logs" :key="index" :class="log.type">
          {{ log.timestamp }} - {{ log.message }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DebugApiTest',
  data() {
    return {
      testResult: '',
      logs: []
    }
  },
  methods: {
    addLog(message, type = 'info') {
      this.logs.unshift({
        timestamp: new Date().toLocaleTimeString(),
        message: message,
        type: type
      });
      // 只保留最近50条日志
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50);
      }
    },
    
    async testDataDictionary() {
      this.addLog('开始测试数据字典API...', 'info');

      try {
        // 添加更多调试信息
        this.addLog('发送POST请求到 /api/get_cmdb_data_dictionary', 'info');
        this.addLog('请求参数: {"dict_code": "B"}', 'info');

        const response = await this.$axios.post('/api/get_cmdb_data_dictionary', {
          dict_code: 'B'
        });

        this.testResult = JSON.stringify({
          success: true,
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          data: response.data
        }, null, 2);
        this.addLog('数据字典API测试成功', 'success');
      } catch (error) {
        this.testResult = JSON.stringify({
          success: false,
          error: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText,
          headers: error.response?.headers,
          data: error.response?.data,
          config: {
            method: error.config?.method,
            url: error.config?.url,
            baseURL: error.config?.baseURL
          }
        }, null, 2);
        this.addLog(`数据字典API测试失败: ${error.message}`, 'error');
        this.addLog(`状态码: ${error.response?.status}`, 'error');
        this.addLog(`状态文本: ${error.response?.statusText}`, 'error');
      }
    },
    
    async testDataDictionaryGET() {
      this.addLog('开始测试数据字典API (GET方法)...', 'info');

      try {
        // 故意使用GET方法来测试405错误
        const response = await this.$axios.get('/api/get_cmdb_data_dictionary?dict_code=B');

        this.testResult = JSON.stringify({
          success: true,
          method: 'GET',
          status: response.status,
          data: response.data
        }, null, 2);
        this.addLog('GET方法测试成功（意外）', 'warning');
      } catch (error) {
        this.testResult = JSON.stringify({
          success: false,
          method: 'GET',
          error: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        }, null, 2);
        this.addLog(`GET方法测试失败（预期）: ${error.message}`, 'info');
        this.addLog(`状态码: ${error.response?.status}`, 'info');
      }
    },

    async testMethodEndpoint() {
      this.addLog('开始测试方法检测API...', 'info');

      try {
        const response = await this.$axios.post('/api/test_method', {
          test: 'data',
          timestamp: new Date().toISOString()
        });

        this.testResult = JSON.stringify(response.data, null, 2);
        this.addLog('方法检测API测试成功', 'success');
        this.addLog(`检测到的nginx信息: ${JSON.stringify(response.data.nginx_info)}`, 'info');
      } catch (error) {
        this.testResult = JSON.stringify({
          error: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        }, null, 2);
        this.addLog(`方法检测API测试失败: ${error.message}`, 'error');
      }
    },

    async testWithDifferentHeaders() {
      this.addLog('开始测试不同请求头的数据字典API...', 'info');

      try {
        const response = await this.$axios.post('/api/get_cmdb_data_dictionary', {
          dict_code: 'B'
        }, {
          headers: {
            'X-Test-Header': 'debug-test',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        this.testResult = JSON.stringify({
          success: true,
          status: response.status,
          data: response.data,
          test_type: 'with_custom_headers'
        }, null, 2);
        this.addLog('自定义请求头测试成功', 'success');
      } catch (error) {
        this.testResult = JSON.stringify({
          success: false,
          error: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          test_type: 'with_custom_headers'
        }, null, 2);
        this.addLog(`自定义请求头测试失败: ${error.message}`, 'error');
      }
    }
  }
}
</script>

<style scoped>
.debug-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.result-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.result-section pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 3px;
  overflow-x: auto;
  max-height: 400px;
}

.log-section {
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.log-content {
  background: #f9f9f9;
  padding: 10px;
  border-radius: 3px;
  max-height: 300px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 12px;
}

.log-content .info {
  color: #333;
}

.log-content .success {
  color: #67c23a;
}

.log-content .error {
  color: #f56c6c;
}

.log-content .warning {
  color: #e6a23c;
}
</style>
