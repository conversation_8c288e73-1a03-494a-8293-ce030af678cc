import { createRouter, createWebHistory } from 'vue-router'
import home from '../views/home.vue'
import login from '../components/login.vue'
import { ElMessage } from "element-plus";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: login,
    },
    {
      path: '/login',
      name: 'login',
      component: login,
      meta: { hidden: true }
    },
    {
      path: '/home',
      name: 'home',
      component: home,
      meta: { requiresAuth: true }, // 需要身份验证
      children: [
        {
          path: '/dashboard',
          name:'dashboard',
          component: () => import('../views/dashboard.vue'),
          meta: { title: '仪表盘' },
        },
        {
          path: '/global_search',
          name:'global_search',
          component: () => import('../views/global_search.vue'),
          meta: { title: '全局搜索' },
        },
        {
          path: '/cmdb_monitored_ip_list',
          name:'cmdb_monitored_ip_list',
          component: () => import('../views/cmdb_monitored_ip_list.vue'),
          meta: { title: '监控IP列表' },
        },
        {
          path: '/cmdb_device_management',
          name:'cmdb_device_management',
          component: () => import('../views/cmdb_device_management.vue'),
          meta: { title: '网络设备' },
        },
        {
          path: '/cmdb_server_management',
          name:'cmdb_server_management',
          component: () => import('../views/cmdb_server_management.vue'),
          meta: { title: '实体服务器' },
        },
        {
          path: '/cmdb_vm_registry',
          name:'cmdb_vm_registry',
          component: () => import('../views/cmdb_vm_registry.vue'),
          meta: { title: '虚拟机信息' },
        },
        {
          path: '/cmdb_application_system_info',
          name:'cmdb_application_system_info',
          component: () => import('../views/cmdb_application_system_info.vue'),
          meta: { title: '应用系统信息' },
        },
        {
          path: '/cmdb_host_scan_results',
          name:'cmdb_host_scan_results',
          component: () => import('../views/cmdb_host_scan_results.vue'),
          meta: { title: 'IP资产管理' },
        },
        {
          path: '/cmdb_discovery_tasks',
          name:'cmdb_discovery_tasks',
          component: () => import('../views/cmdb_discovery_tasks.vue'),
          meta: { title: '发现任务管理' },
        },
        {
          path: '/cmdb_discovery_results',
          name:'cmdb_discovery_results',
          component: () => import('../views/cmdb_discovery_results.vue'),
          meta: { title: '发现结果查看' },
        },
        {
          path: '/cmdb_schedule_tasks',
          name:'cmdb_schedule_tasks',
          component: () => import('../views/cmdb_schedule_tasks.vue'),
          meta: { title: '调度任务管理' },
        },
        {
          path: '/cmdb_schedule_task_detail',
          name:'cmdb_schedule_task_detail',
          component: () => import('../views/cmdb_schedule_task_detail.vue'),
          meta: { title: '调度任务详情' },
        },
        {
          path: '/cmdb_system_admin_responsibility_company',
          name:'cmdb_system_admin_responsibility_company',
          component: () => import('../views/cmdb_system_admin_responsibility_company.vue'),
          meta: { title: '系统管理员责任表（公司）' },
        },
        {
          path: '/cmdb_system_admin_responsibility',
          name:'cmdb_system_admin_responsibility',
          component: () => import('../views/cmdb_system_admin_responsibility.vue'),
          meta: { title: '系统管理员责任表（外部）' },
        },
        {
          path: '/cmdb_data_dictionary',
          name:'cmdb_data_dictionary',
          component: () => import('../views/cmdb_data_dictionary.vue'),
          meta: { title: '数据字典' },
        },
        {
          path: '/cmdb_users',
          name:'cmdb_users',
          component: () => import('../views/cmdb_users.vue'),
          meta: { title: '用户管理' },
        },
        {
          path: '/cmdb_page_permissions',
          name:'cmdb_page_permissions',
          component: () => import('../views/cmdb_page_permissions.vue'),
          meta: { title: '页面权限管理' },
        },
        {
          path: '/cmdb_issue_collection',
          name:'cmdb_issue_collection',
          component: () => import('../views/cmdb_issue_collection.vue'),
          meta: { title: '需求与问题收集' },
        },

        {
          path: '/report_network_device_age',
          name:'report_network_device_age',
          component: () => import('../views/report_network_device_age.vue'),
          meta: { title: '网络设备年限情况统计' },
        },
        {
          path: '/report_server_age',
          name:'report_server_age',
          component: () => import('../views/report_server_age.vue'),
          meta: { title: '实体服务器年限情况统计' },
        },
        {
          path: '/ai_platform',
          name:'ai_platform',
          component: () => import('../views/ai_platform.vue'),
          meta: { title: 'AI平台' },
        },
        {
          path: '/ai_knowledge_base',
          name:'ai_knowledge_base',
          component: () => import('../views/ai_knowledge_base.vue'),
          meta: { title: '知识海洋（知识库）' },
        },
        {
          path: '/ai_document_editor',
          name:'ai_document_editor',
          component: () => import('../views/ai_document_editor.vue'),
          meta: { title: '文档智能修订' },
        },
        {
          path: '/ops_change_management',
          name:'ops_change_management',
          component: () => import('../views/ops_change_management/index.vue'),
          meta: { title: '变更管理' },
        },
        {
          path: '/ops_change_management/detail/:id',
          name:'ops_change_management_detail',
          component: () => import('../views/ops_change_management/detail.vue'),
          meta: { title: '变更详情', activeMenu: '/ops_change_management' },
        },
        {
          path: '/ops_change_templates',
          name:'ops_change_templates',
          component: () => import('../views/ops_change_templates/index.vue'),
          meta: { title: '变更模板管理' },
        },
        {
          path: '/ops_event_management',
          name:'ops_event_management',
          component: () => import('../views/ops_event_management.vue'),
          meta: { title: '事件管理' },
        },
        {
          path: '/ops_calendar',
          name:'ops_calendar',
          component: () => import('../views/ops_calendar.vue'),
          meta: { title: '交易日历' },
        },
        {
          path: '/debug_api_test',
          name:'debug_api_test',
          component: () => import('../views/debug_api_test.vue'),
          meta: { title: 'API调试测试' },
        },
        {
          path: '/static_resource_test',
          name:'static_resource_test',
          component: () => import('../views/static_resource_test.vue'),
          meta: { title: '静态资源测试' },
        }
      ]
    }
  ]
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  const authToken = localStorage.getItem('authToken');
  const tokenTimestamp = localStorage.getItem('tokenTimestamp');

  // 验证token有效期（从localStorage获取配置）
  const tokenExpiresIn = localStorage.getItem('tokenExpiresIn') || '6h';
  const expiresInHours = tokenExpiresIn.includes('h') ?
    parseInt(tokenExpiresIn) :
    parseInt(tokenExpiresIn) / 3600;
  const isTokenValid = authToken && tokenTimestamp &&
    (Date.now() - parseInt(tokenTimestamp)) < expiresInHours * 3600 * 1000;

  if (requiresAuth) {
    if (!authToken || !isTokenValid) {
      localStorage.removeItem('authToken');
      localStorage.removeItem('tokenTimestamp');
      // alert("登陆已过期，请重新登陆")
      ElMessage.error({
        message: '登录已过期，请重新登录',
        duration: 5000, // 显示时长为5秒
        showClose: true, // 显示关闭按钮
      });
      next({ name: 'login' });
    } else {
      // 剩余30分钟时自动刷新token
      const refreshThreshold = (expiresInHours * 3600 - 1800) * 1000;
      if ((Date.now() - parseInt(tokenTimestamp)) > refreshThreshold) {
        localStorage.setItem('tokenTimestamp', Date.now().toString());
      }

      // 检查页面权限
      const username = localStorage.getItem('loginUsername');
      const pagePermissions = JSON.parse(localStorage.getItem('pagePermissions') || '[]');

      // admin 用户有所有页面的权限
      if (username === 'admin') {
        next();
        return;
      }

      // 检查是否有权限访问当前页面
      const hasPermission = pagePermissions.some(permission => {
        return to.path === permission.page_path || to.path.startsWith(permission.page_path + '/');
      });

      if (hasPermission) {
        next();
      } else {
        ElMessage.error({
          message: '无权限访问该页面',
          duration: 5000,
          showClose: true,
        });
        next('/dashboard'); // 重定向到仪表盘页面
      }
    }
  } else {
    next();
  }
});

export default router
