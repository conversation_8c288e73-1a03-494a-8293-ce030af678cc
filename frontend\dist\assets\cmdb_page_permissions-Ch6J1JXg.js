import{_ as K,X as A,Y as D,v as B,c as y,a as s,x as S,w as a,B as L,f as r,b as o,h as u,C as E,m as N,t as m,E as i,p as q,q as R,e as g}from"./index-BnaD8Tdo.js";const j={components:{Search:B,User:D,Check:A},data(){return{loading:!1,selectedUser:null,userList:[],pageTree:[],userPermissions:[],dialogVisible:{selectUser:!1},userSearch:{username:"",real_name:"",total:0,pageSize:10,currentPage:1}}},mounted(){this.loadPageTree()},methods:{async loadPageTree(){try{this.loading=!0,console.log("开始加载页面树形结构");const e=await this.$axios.post("/api/get_page_tree");console.log("页面树形结构响应:",e.data),e.data.code===0?this.pageTree=e.data.msg||[]:(i.error(e.data.msg||"加载失败"),this.pageTree=[])}catch(e){console.error("加载页面树形结构失败:",e),i.error("加载页面树形结构失败"),this.pageTree=[]}finally{this.loading=!1}},showUserSelectDialog(){this.dialogVisible.selectUser=!0,this.searchUsers()},async searchUsers(){try{this.loading=!0,console.log("开始查询用户列表");const e={username:this.userSearch.username,real_name:this.userSearch.real_name,currentPage:this.userSearch.currentPage,pageSize:this.userSearch.pageSize,sortProp:"username",sortOrder:"asc",loginUsername:localStorage.getItem("loginUsername")};console.log("请求参数:",e);const t=await this.$axios.post("/api/get_cmdb_users",e);console.log("查询结果:",t.data),t.data.code===0?(this.userList=t.data.msg||[],this.userSearch.total=t.data.total||0):(i.error(t.data.msg||"查询失败"),this.userList=[],this.userSearch.total=0)}catch(e){console.error("查询用户列表失败:",e),i.error("查询用户列表失败"),this.userList=[],this.userSearch.total=0}finally{this.loading=!1}},handleUserPageSizeChange(e){this.userSearch.pageSize=parseInt(e),this.userSearch.currentPage=1,this.searchUsers()},handleUserPageChange(e){this.userSearch.currentPage=e,this.searchUsers()},async handleUserSelect(e){this.selectedUser=e,this.dialogVisible.selectUser=!1,await this.loadUserPermissions()},async loadUserPermissions(){if(this.selectedUser)try{this.loading=!0,console.log("开始加载用户页面权限:",this.selectedUser.id),this.userPermissions=[],this.$refs.permissionTree&&this.$refs.permissionTree.setCheckedKeys([]);const e=await this.$axios.post("/api/get_user_page_permissions",{userId:this.selectedUser.id});console.log("用户页面权限响应:",e.data),e.data.code===0?(this.userPermissions=e.data.msg||[],console.log("用户权限数量:",this.userPermissions.length),this.$refs.permissionTree&&this.$nextTick(()=>{this.$refs.permissionTree.setCheckedKeys(this.userPermissions)})):(i.error(e.data.msg||"加载失败"),this.userPermissions=[])}catch(e){console.error("加载用户页面权限失败:",e),i.error("加载用户页面权限失败"),this.userPermissions=[]}finally{this.loading=!1}},checkAll(){try{const e=this.getAllKeys(this.pageTree);console.log("全选节点数量:",e.length),this.$refs.permissionTree.setCheckedKeys(e)}catch(e){console.error("全选操作失败:",e),i.error("全选操作失败")}},uncheckAll(){try{this.$refs.permissionTree.setCheckedKeys([]),console.log("已清空所有选中节点")}catch(e){console.error("清空操作失败:",e),i.error("清空操作失败")}},getAllKeys(e){let t=[];return e.forEach(n=>{t.push(n.id),n.children&&n.children.length>0&&(t=t.concat(this.getAllKeys(n.children)))}),t},async savePermissions(){var e;if(!this.selectedUser){i.warning("请先选择用户");return}try{this.loading=!0;const t=this.$refs.permissionTree.getCheckedKeys();console.log("选中的页面权限:",t);const n=await this.$axios.post("/api/update_user_page_permissions",{userId:this.selectedUser.id,pageIds:t,usernameby:localStorage.getItem("loginUsername")});n.data&&n.data.code===0?(i.success("保存权限设置成功"),await this.loadUserPermissions()):i.error(((e=n.data)==null?void 0:e.msg)||"保存权限设置失败")}catch(t){console.error("保存权限设置失败:",t),i.error("保存权限设置失败")}finally{this.loading=!1}}}},d=e=>(q("data-v-95aa7089"),e=e(),R(),e),M={class:"page-permissions"},O={class:"pagination"},X={class:"dialog-footer"},Y={key:0,class:"user-info"},F=d(()=>r("div",{class:"user-title"},"当前选择的用户",-1)),G={class:"user-detail"},H={class:"detail-item"},J=d(()=>r("span",{class:"label"},"用户名:",-1)),Q={class:"value"},W={class:"detail-item"},Z=d(()=>r("span",{class:"label"},"用户姓名:",-1)),$={class:"value"},ee={class:"detail-item"},se=d(()=>r("span",{class:"label"},"联系电话:",-1)),te={class:"value"},re={class:"detail-item"},le=d(()=>r("span",{class:"label"},"电子邮箱:",-1)),ae={class:"value"},oe={class:"detail-item"},ie=d(()=>r("span",{class:"label"},"权限:",-1)),ne={class:"value"},ce={key:1,class:"permission-tree"},de={class:"tree-title"},he=d(()=>r("span",null,"页面权限设置",-1)),_e={class:"tree-container"},ue={key:2,style:{"text-align":"center","margin-top":"100px",color:"#909399"}},pe=d(()=>r("p",null,"请先选择一个用户进行权限设置",-1));function me(e,t,n,ge,l,c){const k=o("el-input"),f=o("el-form-item"),v=o("Search"),U=o("el-icon"),h=o("el-button"),b=o("el-form"),p=o("el-table-column"),P=o("el-table"),V=o("el-pagination"),w=o("el-dialog"),C=o("User"),T=o("Check"),x=o("el-card"),z=o("el-tree"),I=E("loading");return g(),y("div",M,[s(w,{modelValue:l.dialogVisible.selectUser,"onUpdate:modelValue":t[3]||(t[3]=_=>l.dialogVisible.selectUser=_),title:"选择用户",width:"800","align-center":""},{footer:a(()=>[r("div",X,[s(h,{onClick:t[2]||(t[2]=_=>l.dialogVisible.selectUser=!1)},{default:a(()=>[u("取消")]),_:1})])]),default:a(()=>[s(b,{inline:!0},{default:a(()=>[s(f,{label:"用户名："},{default:a(()=>[s(k,{modelValue:l.userSearch.username,"onUpdate:modelValue":t[0]||(t[0]=_=>l.userSearch.username=_),placeholder:"请输入用户名",clearable:""},null,8,["modelValue"])]),_:1}),s(f,{label:"用户姓名："},{default:a(()=>[s(k,{modelValue:l.userSearch.real_name,"onUpdate:modelValue":t[1]||(t[1]=_=>l.userSearch.real_name=_),placeholder:"请输入用户姓名",clearable:""},null,8,["modelValue"])]),_:1}),s(f,null,{default:a(()=>[s(h,{type:"primary",onClick:c.searchUsers},{default:a(()=>[s(U,null,{default:a(()=>[s(v)]),_:1}),u("查询 ")]),_:1},8,["onClick"])]),_:1})]),_:1}),L((g(),N(P,{data:l.userList,border:"",stripe:"",onRowClick:c.handleUserSelect,style:{width:"100%"}},{default:a(()=>[s(p,{prop:"username",label:"用户名"}),s(p,{prop:"real_name",label:"用户姓名"}),s(p,{prop:"phone",label:"联系电话"}),s(p,{prop:"email",label:"电子邮箱"}),s(p,{prop:"role_code_name",label:"权限"})]),_:1},8,["data","onRowClick"])),[[I,l.loading]]),r("div",O,[s(V,{background:"","current-page":l.userSearch.currentPage,"page-size":l.userSearch.pageSize,total:l.userSearch.total,"page-sizes":[10,20,50,100],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:c.handleUserPageSizeChange,onCurrentChange:c.handleUserPageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1},8,["modelValue"]),s(x,{class:"search-card"},{default:a(()=>[s(b,{inline:!0},{default:a(()=>[s(f,null,{default:a(()=>[s(h,{type:"primary",onClick:c.showUserSelectDialog},{default:a(()=>[s(U,null,{default:a(()=>[s(C)]),_:1}),u("选择用户 ")]),_:1},8,["onClick"]),s(h,{type:"success",onClick:c.savePermissions,disabled:!l.selectedUser},{default:a(()=>[s(U,null,{default:a(()=>[s(T)]),_:1}),u("保存权限设置 ")]),_:1},8,["onClick","disabled"])]),_:1})]),_:1})]),_:1}),l.selectedUser?(g(),y("div",Y,[F,r("div",G,[r("div",H,[J,r("span",Q,m(l.selectedUser.username),1)]),r("div",W,[Z,r("span",$,m(l.selectedUser.real_name||"未设置"),1)]),r("div",ee,[se,r("span",te,m(l.selectedUser.phone||"未设置"),1)]),r("div",re,[le,r("span",ae,m(l.selectedUser.email||"未设置"),1)]),r("div",oe,[ie,r("span",ne,m(l.selectedUser.role_code_name),1)])])])):S("",!0),l.selectedUser?(g(),y("div",ce,[r("div",de,[he,r("div",null,[s(h,{type:"primary",size:"small",onClick:c.checkAll},{default:a(()=>[u("全选")]),_:1},8,["onClick"]),s(h,{type:"info",size:"small",onClick:c.uncheckAll},{default:a(()=>[u("清空")]),_:1},8,["onClick"])])]),r("div",_e,[s(z,{ref:"permissionTree",data:l.pageTree,"show-checkbox":"","check-strictly":"","node-key":"id",props:{label:"page_name",children:"children"}},null,8,["data"])])])):S("",!0),l.selectedUser?S("",!0):(g(),y("div",ue,[s(U,{style:{"font-size":"48px","margin-bottom":"20px"}},{default:a(()=>[s(C)]),_:1}),pe]))])}const Ue=K(j,[["render",me],["__scopeId","data-v-95aa7089"]]);export{Ue as default};
