import{_ as Y,D as J,z as K,j as W,G as X,H as Z,A as $,c as w,a as e,f as b,w as l,b as c,h as o,m as f,x as D,t as y,B as A,C as ee,F as R,p as te,q as se,e as u}from"./index-BnaD8Tdo.js";const ae={name:"cmdb_discovery_tasks",components:{Plus:$,Monitor:Z,VideoPlay:X,RefreshRight:W,Download:K,QuestionFilled:J},data(){var r,a,d;return{taskList:[],loading:!1,hasDeletePermission:(r=localStorage.getItem("role_code"))==null?void 0:r.includes("D"),hasUpdatePermission:(a=localStorage.getItem("role_code"))==null?void 0:a.includes("U"),hasInsertPermission:(d=localStorage.getItem("role_code"))==null?void 0:d.includes("I"),statusPollingTimer:null,runningTaskIds:[],selectedTasks:[],queueStatus:{queuedTasks:[],runningTasks:[],queueLength:0,runningCount:0,maxConcurrent:3,systemStatus:"idle"},queueStatusLoading:!1,queueStatusTimer:null,lastRefreshTime:0,dialogVisible:{edit:!1,delete:!1,test:!1,queueStatus:!1},search:{task_name:"",task_type:"",status:"",schedule_task_id:"",total:0,pageSize:10,currentPage:1,sortProp:"created_at",sortOrder:"desc"},formData:{id:null,task_name:"",task_type:"network_scan",description:"",ip_range_type:"range",ip_range_start:"",ip_range_end:"",ip_cidr:"",scan_ports:"22,80,443,3389",schedule_type:"manual",schedule_value:""},formRules:{task_name:[{required:!0,message:"请输入任务名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],task_type:[{required:!0,message:"请选择任务类型",trigger:"change"}],ip_range_type:[{required:!0,message:"请选择IP范围类型",trigger:"change"}],ip_range_start:[{required:!0,message:"请输入起始IP地址",trigger:"blur"},{pattern:/^(\d{1,3}\.){3}\d{1,3}$/,message:"请输入有效的IP地址",trigger:"blur"}],ip_range_end:[{required:!0,message:"请输入结束IP地址",trigger:"blur"},{pattern:/^(\d{1,3}\.){3}\d{1,3}$/,message:"请输入有效的IP地址",trigger:"blur"}],ip_cidr:[{required:!0,message:"请输入CIDR网段",trigger:"blur"},{pattern:/^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/,message:"请输入有效的CIDR格式，如***********/24",trigger:"blur"}]},testScanForm:{scanType:"range",startIp:"***********",endIp:"***********0",cidr:"***********/24",timeout:1e3},testScanRules:{startIp:[{required:!0,message:"请输入起始IP地址",trigger:"blur"},{pattern:/^(\d{1,3}\.){3}\d{1,3}$/,message:"请输入有效的IP地址",trigger:"blur"}],endIp:[{required:!0,message:"请输入结束IP地址",trigger:"blur"},{pattern:/^(\d{1,3}\.){3}\d{1,3}$/,message:"请输入有效的IP地址",trigger:"blur"}],cidr:[{required:!0,message:"请输入CIDR网段",trigger:"blur"},{pattern:/^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/,message:"请输入有效的CIDR格式",trigger:"blur"}],timeout:[{required:!0,message:"请输入超时时间",trigger:"blur"}]},testScanResults:[],testScanLoading:!1}},mounted(){this.loadData(),this.startStatusPolling()},beforeUnmount(){this.stopStatusPolling()},methods:{async loadData(){try{this.loading=!0,console.log("开始加载任务列表..."),this.taskList=[];const r=await this.$axios.post("/api/discovery/get_discovery_tasks",this.search,{timeout:3e4});if(!r||!r.data){console.error("加载任务列表失败: 无效的响应"),this.$message.error("加载任务列表失败: 无效的响应"),this.loading=!1;return}if(r.data.code!==0){console.error("加载任务列表失败:",r.data.msg),this.$message.error(r.data.msg||"加载任务列表失败"),this.loading=!1;return}Array.isArray(r.data.msg)?(console.log("任务列表加载成功，数量:",r.data.msg.length),this.taskList=r.data.msg,this.search.total=r.data.total||0):(console.warn("响应数据不是数组，使用空数组代替"),this.taskList=[],this.search.total=0)}catch(r){console.error("加载任务列表失败:",r),this.$message.error("加载任务列表失败"),this.taskList=[],this.search.total=0}finally{this.loading=!1}},resetSearch(){this.search={task_name:"",task_type:"",status:"",schedule_task_id:"",total:0,pageSize:10,currentPage:1,sortProp:"created_at",sortOrder:"desc"},this.loadData()},handlePageChange(r){this.search.currentPage=r,this.loadData()},handleSelectionChange(r){if(!r||!Array.isArray(r)){console.warn("选择变化时，selection 不是数组:",r),this.selectedTasks=[];return}this.selectedTasks=r},handlePageSizeChange(r){this.search.pageSize=r,this.search.currentPage=1,this.loadData()},handleSortChange({prop:r,order:a}){r&&(this.search.sortProp=r,this.search.sortOrder=a==="ascending"?"asc":"desc",this.loadData())},formatDateTime(r){if(!r)return"";if(typeof r=="string"&&r.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/))return r;try{const a=new Date(r);if(isNaN(a.getTime()))return r;const d=a.getFullYear(),k=String(a.getMonth()+1).padStart(2,"0"),s=String(a.getDate()).padStart(2,"0"),i=String(a.getHours()).padStart(2,"0"),g=String(a.getMinutes()).padStart(2,"0"),n=String(a.getSeconds()).padStart(2,"0");return`${d}-${k}-${s} ${i}:${g}:${n}`}catch(a){return console.error("Date formatting error:",a),r}},formatDuration(r){if(!r||r<=0)return"-";if(r<60)return`${r}秒`;if(r<3600){const i=Math.floor(r/60),g=r%60;return`${i}分${g>0?g+"秒":""}`}const a=Math.floor(r/3600),d=Math.floor(r%3600/60),k=r%60;let s=`${a}小时`;return d>0&&(s+=`${d}分`),k>0&&(s+=`${k}秒`),s},handleAdd(){this.formData={id:null,task_name:"",task_type:"network_scan",description:"",ip_range_type:"range",ip_range_start:"",ip_range_end:"",ip_cidr:"",scan_ports:"22,80,443,3389",schedule_type:"manual",schedule_value:""},this.dialogVisible.edit=!0},handleEdit(r){this.formData={...r},this.formData.ip_range_type||(this.formData.ip_range_type=this.formData.ip_cidr?"cidr":"range"),this.formData.schedule_type="manual",this.formData.schedule_value="",this.dialogVisible.edit=!0},handleDelete(r){this.formData={...r},this.dialogVisible.delete=!0},async handleRun(r){try{const a=await this.$axios.post("/api/discovery/run_discovery_task",{id:r.id,usernameby:localStorage.getItem("loginUsername")||"unknown"});a.data.code===0?(this.$message.success("任务已开始执行，请稍后查看结果"),this.runningTaskIds.includes(r.id)||this.runningTaskIds.push(r.id),r.status="running",setTimeout(()=>{this.loadData()},1e3)):this.$message.error(a.data.msg||"执行任务失败")}catch(a){console.error("执行任务失败:",a),this.$message.error("执行任务失败")}},async handleStop(r){try{const a=await this.$axios.post("/api/discovery/stop_discovery_task",{id:r.id,usernameby:localStorage.getItem("loginUsername")||"unknown"});a.data.code===0?(this.$message.success("停止指令已发送，任务正在停止中"),setTimeout(()=>{this.loadData()},1e3)):this.$message.error(a.data.msg||"停止任务失败")}catch(a){console.error("停止任务失败:",a),this.$message.error("停止任务失败")}},async handleResetStatus(r){try{await this.$confirm(`确定要重置任务 "${r.task_name}" 的状态吗？这将把状态从“运行中”改为“失败”。`,"重置状态确认",{confirmButtonText:"确定重置",cancelButtonText:"取消",type:"warning"});const a=await this.$axios.post("/api/discovery/reset_task_status",{id:r.id,status:"failed",usernameby:localStorage.getItem("loginUsername")||"unknown"});if(a.data.code===0){this.$message.success(a.data.msg||"任务状态已重置"),r.status="failed";const d=this.runningTaskIds.indexOf(r.id);d!==-1&&this.runningTaskIds.splice(d,1),this.loadData()}else this.$message.error(a.data.msg||"重置任务状态失败")}catch(a){if(a==="cancel")return;console.error("重置任务状态失败:",a),this.$message.error("重置任务状态失败")}},handleViewResults(r){this.$router.push({path:"/cmdb_discovery_results",query:{task_id:r.id}})},async submitTaskForm(){try{this.formData.ip_range_type==="range"?await this.$refs.taskFormRef.validateField(["task_name","task_type","ip_range_type","ip_range_start","ip_range_end"]):await this.$refs.taskFormRef.validateField(["task_name","task_type","ip_range_type","ip_cidr"]);const r=this.formData.id?"/api/discovery/update_discovery_task":"/api/discovery/add_discovery_task",a={...this.formData,schedule_type:"manual",schedule_value:"",usernameby:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post(r,a),this.$message.success(this.formData.id?"更新成功":"添加成功"),this.dialogVisible.edit=!1,this.loadData()}catch(r){console.error("提交任务失败:",r),this.$message.error("提交失败，请检查表单")}},async submitDelete(){try{await this.$axios.post("/api/discovery/del_discovery_task",{id:this.formData.id,usernameby:localStorage.getItem("loginUsername")||"unknown"}),this.$message.success("删除成功"),this.dialogVisible.delete=!1,this.loadData()}catch(r){console.error("删除任务失败:",r),this.$message.error("删除失败")}},handleTestScan(){this.testScanResults=[],this.dialogVisible.test=!0},async runTestScan(){try{this.testScanForm.scanType==="range"?await this.$refs.testScanFormRef.validateField(["startIp","endIp","timeout"]):await this.$refs.testScanFormRef.validateField(["cidr","timeout"]),this.testScanLoading=!0,this.testScanResults=[];const r={timeout:this.testScanForm.timeout};this.testScanForm.scanType==="range"?(r.startIp=this.testScanForm.startIp,r.endIp=this.testScanForm.endIp):r.cidr=this.testScanForm.cidr;const a=await this.$axios.post("/api/discovery/test_network_scan",r,{timeout:6e4});this.testScanResults=a.data.msg,this.testScanResults.length===0?this.$message.warning("未发现任何设备"):this.$message.success(`发现 ${this.testScanResults.length} 个设备`)}catch(r){console.error("测试扫描失败:",r),this.$message.error("测试扫描失败")}finally{this.testScanLoading=!1}},startStatusPolling(){this.statusPollingTimer=setInterval(()=>{this.pollRunningTasksStatus()},3e3),this.pollRunningTasksStatus()},stopStatusPolling(){this.statusPollingTimer&&(clearInterval(this.statusPollingTimer),this.statusPollingTimer=null)},async pollRunningTasksStatus(){if(!this.taskList||!Array.isArray(this.taskList)){console.warn("轮询任务状态时，taskList 不是数组:",this.taskList);return}const r=this.taskList.filter(i=>i&&i.status==="running");if(r.length===0)return;const a=new Date,d=r.map(i=>this.$axios.post("/api/discovery/get_task_status",{id:i.id},{timeout:3e4}).catch(g=>(console.error(`获取任务 ${i.id} 状态失败:`,g),g.code==="ECONNABORTED"?{data:{code:1,msg:"timeout",isTimeout:!0}}:{data:{code:1}}))),k=await Promise.all(d);let s=!1;k.forEach((i,g)=>{if(i.data.code===0){const n=i.data.msg,_=r[g];if(n.isRunning){if(n.lastRunTime){const m=new Date(n.lastRunTime);a-m>10*60*1e3&&(console.log(`任务 ${_.id} 运行时间过长，尝试自动重置`),this.autoResetTaskStatus(_),s=!0)}}else{const m=this.runningTaskIds.indexOf(_.id);m!==-1&&this.runningTaskIds.splice(m,1),_.status!==n.status&&(s=!0)}}}),s&&this.loadData()},async autoResetTaskStatus(r){try{const a=await this.$axios.post("/api/discovery/reset_task_status",{id:r.id,status:"failed",usernameby:"system_auto_reset"});if(a.data.code===0){console.log(`自动重置任务 ${r.id} 状态成功`);const d=this.runningTaskIds.indexOf(r.id);d!==-1&&this.runningTaskIds.splice(d,1)}else console.error(`自动重置任务 ${r.id} 状态失败:`,a.data.msg)}catch(a){console.error(`自动重置任务 ${r.id} 状态失败:`,a)}},exportData(){const a=this.$refs.table.columns.filter(_=>_.type!=="selection"),d=a.map(_=>_.label),k=this.taskList.map(_=>a.map(m=>m.property==="status"?{inactive:"未激活",queued:"排队中",running:"运行中",completed:"已完成",failed:"失败"}[_[m.property]]||_[m.property]:m.property==="task_type"?{network_scan:"网络扫描",port_scan:"端口扫描"}[_[m.property]]||_[m.property]:m.property==="last_run_time"?this.formatDateTime(_[m.property]):_[m.property]||""));let s=d.join(",")+`
`;k.forEach(_=>{s+=_.join(",")+`
`});const i=new Blob([s],{type:"text/csv;charset=utf-8;"}),g=URL.createObjectURL(i),n=document.createElement("a");n.setAttribute("href",g),n.setAttribute("download",`发现任务列表_${new Date().toISOString().slice(0,10)}.csv`),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)},async handleBatchRun(){var r,a;if(this.selectedTasks.length===0){this.$message.warning("请先选择要执行的任务");return}try{await this.$confirm(`确定要批量执行选中的 ${this.selectedTasks.length} 个任务吗？

注意：为了系统性能考虑，任务将被添加到队列中并按照并发限制执行。`,"批量执行任务",{confirmButtonText:"确定执行",cancelButtonText:"取消",type:"warning"});const d=this.selectedTasks.map(s=>s.id),k=await this.$axios.post("/api/discovery/run_batch_discovery_tasks",{taskIds:d,usernameby:localStorage.getItem("loginUsername")||"admin"});k.data.code===0?(this.$message.success(k.data.msg),this.showQueueStatus(),setTimeout(()=>{this.loadData()},1e3)):this.$message.error(k.data.msg)}catch(d){if(d==="cancel")return;console.error("批量执行任务失败:",d),this.$message.error(((a=(r=d.response)==null?void 0:r.data)==null?void 0:a.msg)||"批量执行任务失败")}},async showQueueStatus(){try{await this.refreshQueueStatus(),this.dialogVisible.queueStatus=!0,this.startQueueStatusPolling()}catch(r){r.message==="refresh_too_frequent"&&(this.dialogVisible.queueStatus=!0,this.startQueueStatusPolling())}},async refreshQueueStatus(){var d,k;const r=Date.now(),a=1e4;if(r-this.lastRefreshTime<a){if(!this.queueStatusTimer){const s=Math.ceil((a-(r-this.lastRefreshTime))/1e3);return this.$message.warning(`请等待 ${s} 秒后再刷新`),Promise.reject(new Error("refresh_too_frequent"))}return Promise.resolve()}try{this.queueStatusLoading=!0,this.lastRefreshTime=r;const s=await this.$axios.post("/api/discovery/get_task_queue_status",{},{timeout:3e4});return s.data.code===0?(this.queueStatus=s.data.msg,Promise.resolve()):(this.$message.error(s.data.msg||"获取队列状态失败"),Promise.reject(new Error(s.data.msg||"failed_to_get_status")))}catch(s){return console.error("获取队列状态失败:",s),s.code==="ECONNABORTED"?this.$message.error("获取队列状态超时，请稍后重试"):this.$message.error(((k=(d=s.response)==null?void 0:d.data)==null?void 0:k.msg)||"获取队列状态失败"),Promise.reject(s)}finally{this.queueStatusLoading=!1}},startQueueStatusPolling(){this.stopQueueStatusPolling(),this.queueStatusTimer=setInterval(()=>{if(!this.dialogVisible.queueStatus){this.stopQueueStatusPolling();return}this.refreshQueueStatus().then(()=>{this.queueStatus.queueLength===0&&this.queueStatus.runningCount===0&&(this.stopQueueStatusPolling(),this.loadData(),this.$message.success("所有任务已完成"),this.$notify({title:"任务已完成",message:"所有任务已完成，您可以点击对话框中的“关闭”按钮关闭此窗口",type:"success",duration:5e3}))}).catch(r=>{console.error("轮询队列状态失败:",r)})},3e4)},stopQueueStatusPolling(){this.queueStatusTimer&&(clearInterval(this.queueStatusTimer),this.queueStatusTimer=null)}},beforeUnmount(){this.statusPollingTimer&&clearInterval(this.statusPollingTimer),this.stopQueueStatusPolling()}},F=r=>(te("data-v-6a4268ce"),r=r(),se(),r),le={class:"discovery-tasks"},re=F(()=>b("div",{class:"page-header"},[b("p",{class:"description",style:{"line-height":"1.5","margin-bottom":"10px",color:"#E6A23C","background-color":"#FDF6EC",padding:"8px 12px","border-radius":"4px","border-left":"4px solid #E6A23C"}}," 创建和管理网络设备自动发现任务，支持IP范围扫描和端口检测 ")],-1)),oe={class:"button-container"},ie={class:"action-bar unified-action-bar"},ne={class:"action-bar-left"},ue={class:"action-bar-right"},de={key:2},ce={key:0},me={key:1},pe={key:5},ge={key:1},_e={key:1},he={class:"pagination-container"},fe={class:"dialog-footer"},ye={class:"dialog-footer"},ke={key:0,class:"test-scan-results"},be=F(()=>b("h3",null,"扫描结果",-1)),Se={class:"dialog-footer"},we={class:"queue-status-container"},De=F(()=>b("h3",null,"正在运行的任务",-1)),Ie={key:0,class:"task-count"},Ve={class:"dialog-footer"};function ve(r,a,d,k,s,i){const g=c("el-input"),n=c("el-form-item"),_=c("el-col"),m=c("el-option"),I=c("el-select"),h=c("el-button"),E=c("el-row"),q=c("el-form"),U=c("el-card"),B=c("Plus"),V=c("el-icon"),M=c("Monitor"),O=c("VideoPlay"),L=c("RefreshRight"),N=c("Download"),p=c("el-table-column"),S=c("el-tag"),j=c("el-tooltip"),C=c("el-table"),H=c("el-pagination"),T=c("el-radio"),z=c("el-radio-group"),x=c("el-alert"),P=c("el-dialog"),G=c("el-input-number"),Q=ee("loading");return u(),w("div",le,[re,e(U,{class:"search-card"},{default:l(()=>[e(q,{inline:!0,class:"search-form"},{default:l(()=>[e(E,{gutter:10},{default:l(()=>[e(_,{xs:24,sm:12,md:6,lg:6},{default:l(()=>[e(n,{label:"任务名称",class:"form-item-with-label"},{default:l(()=>[e(g,{modelValue:s.search.task_name,"onUpdate:modelValue":a[0]||(a[0]=t=>s.search.task_name=t),placeholder:"请输入任务名称",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{xs:24,sm:12,md:6,lg:6},{default:l(()=>[e(n,{label:"任务类型",class:"form-item-with-label"},{default:l(()=>[e(I,{modelValue:s.search.task_type,"onUpdate:modelValue":a[1]||(a[1]=t=>s.search.task_type=t),placeholder:"请选择任务类型",clearable:"",style:{width:"100%"},"popper-class":"task-type-select"},{default:l(()=>[e(m,{label:"网络扫描",value:"network_scan"}),e(m,{label:"端口扫描",value:"port_scan"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{xs:24,sm:12,md:6,lg:6},{default:l(()=>[e(n,{label:"状态",class:"form-item-with-label"},{default:l(()=>[e(I,{modelValue:s.search.status,"onUpdate:modelValue":a[2]||(a[2]=t=>s.search.status=t),placeholder:"请选择状态",clearable:"",style:{width:"100%"},"popper-class":"status-select"},{default:l(()=>[e(m,{label:"未激活",value:"inactive"}),e(m,{label:"排队中",value:"queued"}),e(m,{label:"运行中",value:"running"}),e(m,{label:"已完成",value:"completed"}),e(m,{label:"失败",value:"failed"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{xs:24,sm:12,md:6,lg:6},{default:l(()=>[e(n,{label:"调度任务ID",class:"form-item-with-label"},{default:l(()=>[e(g,{modelValue:s.search.schedule_task_id,"onUpdate:modelValue":a[3]||(a[3]=t=>s.search.schedule_task_id=t),placeholder:"请输入调度任务ID",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{xs:24,sm:12,md:24,lg:24,class:"search-buttons-col"},{default:l(()=>[e(n,{label:" ",class:"form-item-with-label search-buttons"},{default:l(()=>[b("div",oe,[e(h,{type:"primary",onClick:i.loadData},{default:l(()=>[o("查询")]),_:1},8,["onClick"]),e(h,{onClick:i.resetSearch},{default:l(()=>[o("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),b("div",ie,[b("div",ne,[s.hasInsertPermission?(u(),f(h,{key:0,type:"primary",onClick:i.handleAdd},{default:l(()=>[e(V,null,{default:l(()=>[e(B)]),_:1}),o(" 新增任务 ")]),_:1},8,["onClick"])):D("",!0),e(h,{type:"success",onClick:i.handleTestScan},{default:l(()=>[e(V,null,{default:l(()=>[e(M)]),_:1}),o(" 测试扫描 ")]),_:1},8,["onClick"]),e(h,{type:"warning",onClick:i.handleBatchRun,disabled:s.selectedTasks.length===0},{default:l(()=>[e(V,null,{default:l(()=>[e(O)]),_:1}),o(" 批量执行 ("+y(s.selectedTasks.length)+") ",1)]),_:1},8,["onClick","disabled"]),e(h,{type:"primary",onClick:i.showQueueStatus},{default:l(()=>[e(V,null,{default:l(()=>[e(L)]),_:1}),o(" 查看队列状态 ")]),_:1},8,["onClick"])]),b("div",ue,[e(h,{type:"info",onClick:i.exportData},{default:l(()=>[e(V,null,{default:l(()=>[e(N)]),_:1}),o(" 导出数据 ")]),_:1},8,["onClick"])])]),e(U,{class:"table-card"},{default:l(()=>[A((u(),f(C,{data:s.taskList||[],ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:i.handleSortChange,onSelectionChange:i.handleSelectionChange,style:{width:"100%"},"max-height":600},{default:l(()=>[e(p,{type:"selection",width:"55"}),e(p,{prop:"id",label:"任务ID",width:"100",sortable:""},{default:l(t=>[e(S,{type:"info"},{default:l(()=>[o(y(t.row.id),1)]),_:2},1024)]),_:1}),e(p,{prop:"task_name",label:"任务名称",sortable:"","min-width":"150"}),e(p,{prop:"task_type",label:"任务类型",sortable:"",width:"120"},{default:l(t=>[t.row.task_type==="network_scan"?(u(),f(S,{key:0,type:"primary"},{default:l(()=>[o(" 网络扫描 ")]),_:1})):t.row.task_type==="port_scan"?(u(),f(S,{key:1,type:"success"},{default:l(()=>[o(" 端口扫描 ")]),_:1})):(u(),w("span",de,y(t.row.task_type),1))]),_:1}),e(p,{label:"IP范围",sortable:"","min-width":"150"},{default:l(t=>[t.row.ip_range_type==="cidr"?(u(),f(S,{key:0,type:"info"},{default:l(()=>[o(y(t.row.ip_cidr),1)]),_:2},1024)):(u(),w(R,{key:1},[o(y(t.row.ip_range_start)+" - "+y(t.row.ip_range_end),1)],64))]),_:1}),e(p,{prop:"scan_ports",label:"扫描端口",width:"150"}),e(p,{prop:"last_run_time",label:"上次运行时间",sortable:"",width:"180"},{default:l(t=>[o(y(i.formatDateTime(t.row.last_run_time)),1)]),_:1}),e(p,{prop:"run_duration_seconds",label:"运行时间",sortable:"",width:"100"},{default:l(t=>[t.row.run_duration_seconds>0?(u(),w("span",ce,y(i.formatDuration(t.row.run_duration_seconds)),1)):(u(),w("span",me,"-"))]),_:1}),e(p,{prop:"status",label:"状态",sortable:"",width:"100"},{default:l(t=>[t.row.status==="inactive"?(u(),f(S,{key:0,type:"info"},{default:l(()=>[o(" 未激活 ")]),_:1})):t.row.status==="queued"?(u(),f(S,{key:1,type:"info"},{default:l(()=>[o(" 排队中 ")]),_:1})):t.row.status==="running"?(u(),f(S,{key:2,type:"warning"},{default:l(()=>[o(" 运行中 ")]),_:1})):t.row.status==="completed"?(u(),f(S,{key:3,type:"success"},{default:l(()=>[o(" 已完成 ")]),_:1})):t.row.status==="failed"?(u(),f(S,{key:4,type:"danger"},{default:l(()=>[o(" 失败 ")]),_:1})):(u(),w("span",pe,y(t.row.status),1))]),_:1}),e(p,{prop:"result_count",label:"发现结果数",sortable:"",width:"120"},{default:l(t=>[t.row.result_count>0?(u(),f(S,{key:0,type:"info"},{default:l(()=>[o(y(t.row.result_count),1)]),_:2},1024)):(u(),w("span",ge,"0"))]),_:1}),e(p,{prop:"schedule_task_id",label:"调度任务",width:"150",sortable:""},{default:l(t=>[t.row.schedule_task_id?(u(),f(j,{key:0,content:t.row.schedule_task_name||"调度任务",placement:"top"},{default:l(()=>[e(S,{type:"success"},{default:l(()=>[o(y(t.row.schedule_task_id),1)]),_:2},1024)]),_:2},1032,["content"])):(u(),w("span",_e,"-"))]),_:1}),e(p,{label:"操作",width:"280",align:"center",fixed:"right"},{default:l(t=>[t.row.status!=="running"&&t.row.status!=="queued"?(u(),f(h,{key:0,type:"primary",size:"small",onClick:v=>i.handleRun(t.row),disabled:t.row.status==="running"||t.row.status==="queued",title:"执行任务ID: "+t.row.id},{default:l(()=>[o(" 执行 ")]),_:2},1032,["onClick","disabled","title"])):D("",!0),t.row.status==="running"||t.row.status==="queued"?(u(),f(h,{key:1,type:"danger",size:"small",onClick:v=>i.handleStop(t.row),title:"停止任务ID: "+t.row.id},{default:l(()=>[o(" 停止 ")]),_:2},1032,["onClick","title"])):D("",!0),e(h,{type:"success",size:"small",onClick:v=>i.handleViewResults(t.row),title:"查看任务ID: "+t.row.id+" 的结果"},{default:l(()=>[o(" 查看结果 ")]),_:2},1032,["onClick","title"]),s.hasUpdatePermission&&t.row.status!=="running"&&t.row.status!=="queued"?(u(),f(h,{key:2,type:"warning",size:"small",onClick:v=>i.handleEdit(t.row),title:"编辑任务ID: "+t.row.id},{default:l(()=>[o(" 编辑 ")]),_:2},1032,["onClick","title"])):D("",!0),s.hasDeletePermission&&t.row.status!=="running"&&t.row.status!=="queued"?(u(),f(h,{key:3,type:"danger",size:"small",onClick:v=>i.handleDelete(t.row),title:"删除任务ID: "+t.row.id},{default:l(()=>[o(" 删除 ")]),_:2},1032,["onClick","title"])):D("",!0),t.row.status==="running"||t.row.status==="queued"?(u(),f(h,{key:4,type:"info",size:"small",onClick:v=>i.handleResetStatus(t.row),title:"重置任务ID: "+t.row.id+" 的状态"},{default:l(()=>[o(" 重置状态 ")]),_:2},1032,["onClick","title"])):D("",!0)]),_:1})]),_:1},8,["data","onSortChange","onSelectionChange"])),[[Q,s.loading]]),b("div",he,[e(H,{"current-page":s.search.currentPage,"onUpdate:currentPage":a[4]||(a[4]=t=>s.search.currentPage=t),"page-size":s.search.pageSize,"onUpdate:pageSize":a[5]||(a[5]=t=>s.search.pageSize=t),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:s.search.total,onSizeChange:i.handlePageSizeChange,onCurrentChange:i.handlePageChange,background:""},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),e(P,{modelValue:s.dialogVisible.edit,"onUpdate:modelValue":a[15]||(a[15]=t=>s.dialogVisible.edit=t),title:s.formData.id?"编辑任务 (ID: "+s.formData.id+")":"添加任务",width:"600px","destroy-on-close":""},{footer:l(()=>[b("span",fe,[e(h,{onClick:a[14]||(a[14]=t=>s.dialogVisible.edit=!1)},{default:l(()=>[o("取消")]),_:1}),e(h,{type:"primary",onClick:i.submitTaskForm},{default:l(()=>[o("确定")]),_:1},8,["onClick"])])]),default:l(()=>[e(q,{model:s.formData,ref:"taskFormRef","label-width":"100px",rules:s.formRules},{default:l(()=>[e(n,{label:"任务名称",prop:"task_name"},{default:l(()=>[e(g,{modelValue:s.formData.task_name,"onUpdate:modelValue":a[6]||(a[6]=t=>s.formData.task_name=t),placeholder:"请输入任务名称"},null,8,["modelValue"])]),_:1}),e(n,{label:"任务类型",prop:"task_type"},{default:l(()=>[e(I,{modelValue:s.formData.task_type,"onUpdate:modelValue":a[7]||(a[7]=t=>s.formData.task_type=t),placeholder:"请选择任务类型",style:{width:"100%"},"popper-class":"task-type-select-form"},{default:l(()=>[e(m,{label:"网络扫描",value:"network_scan"}),e(m,{label:"端口扫描",value:"port_scan"})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"描述",prop:"description"},{default:l(()=>[e(g,{modelValue:s.formData.description,"onUpdate:modelValue":a[8]||(a[8]=t=>s.formData.description=t),type:"textarea",placeholder:"请输入任务描述"},null,8,["modelValue"])]),_:1}),e(n,{label:"IP范围类型",prop:"ip_range_type",class:"form-item-with-label"},{default:l(()=>[e(z,{modelValue:s.formData.ip_range_type,"onUpdate:modelValue":a[9]||(a[9]=t=>s.formData.ip_range_type=t),class:"radio-group-full-width"},{default:l(()=>[e(T,{label:"range"},{default:l(()=>[o("起止IP范围")]),_:1}),e(T,{label:"cidr"},{default:l(()=>[o("CIDR网段")]),_:1})]),_:1},8,["modelValue"])]),_:1}),s.formData.ip_range_type==="range"?(u(),w(R,{key:0},[e(n,{label:"IP范围起始",prop:"ip_range_start"},{default:l(()=>[e(g,{modelValue:s.formData.ip_range_start,"onUpdate:modelValue":a[10]||(a[10]=t=>s.formData.ip_range_start=t),placeholder:"请输入起始IP地址"},null,8,["modelValue"])]),_:1}),e(n,{label:"IP范围结束",prop:"ip_range_end"},{default:l(()=>[e(g,{modelValue:s.formData.ip_range_end,"onUpdate:modelValue":a[11]||(a[11]=t=>s.formData.ip_range_end=t),placeholder:"请输入结束IP地址"},null,8,["modelValue"])]),_:1})],64)):(u(),f(n,{key:1,label:"CIDR网段",prop:"ip_cidr"},{default:l(()=>[e(g,{modelValue:s.formData.ip_cidr,"onUpdate:modelValue":a[12]||(a[12]=t=>s.formData.ip_cidr=t),placeholder:"请输入CIDR格式的网段，如***********/24"},null,8,["modelValue"])]),_:1})),e(n,{label:"扫描端口",prop:"scan_ports"},{default:l(()=>[e(g,{modelValue:s.formData.scan_ports,"onUpdate:modelValue":a[13]||(a[13]=t=>s.formData.scan_ports=t),placeholder:"请输入端口列表，如：22,80,443,3389"},null,8,["modelValue"])]),_:1}),e(n,null,{default:l(()=>[e(x,{type:"info",closable:!1,"show-icon":"",title:"调度设置提示",description:"所有发现任务默认为手动调度。如需要自动调度，请在调度任务管理页面进行设置。"})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),e(P,{modelValue:s.dialogVisible.delete,"onUpdate:modelValue":a[17]||(a[17]=t=>s.dialogVisible.delete=t),title:"删除确认",width:"400px","destroy-on-close":""},{footer:l(()=>[b("span",ye,[e(h,{onClick:a[16]||(a[16]=t=>s.dialogVisible.delete=!1)},{default:l(()=>[o("取消")]),_:1}),e(h,{type:"danger",onClick:i.submitDelete},{default:l(()=>[o("确定")]),_:1},8,["onClick"])])]),default:l(()=>[b("p",null,'确定要删除任务 "'+y(s.formData.task_name)+'"(ID: '+y(s.formData.id)+") 吗？",1)]),_:1},8,["modelValue"]),e(P,{modelValue:s.dialogVisible.test,"onUpdate:modelValue":a[24]||(a[24]=t=>s.dialogVisible.test=t),title:"测试网络扫描",width:"500px","destroy-on-close":""},{footer:l(()=>[b("span",Se,[e(h,{onClick:a[23]||(a[23]=t=>s.dialogVisible.test=!1)},{default:l(()=>[o("关闭")]),_:1}),e(h,{type:"primary",onClick:i.runTestScan,loading:s.testScanLoading},{default:l(()=>[o(" 开始扫描 ")]),_:1},8,["onClick","loading"])])]),default:l(()=>[e(q,{model:s.testScanForm,ref:"testScanFormRef","label-width":"100px",rules:s.testScanRules},{default:l(()=>[e(n,{label:"扫描类型",class:"form-item-with-label"},{default:l(()=>[e(z,{modelValue:s.testScanForm.scanType,"onUpdate:modelValue":a[18]||(a[18]=t=>s.testScanForm.scanType=t),class:"radio-group-full-width"},{default:l(()=>[e(T,{label:"range"},{default:l(()=>[o("起止IP范围")]),_:1}),e(T,{label:"cidr"},{default:l(()=>[o("CIDR网段")]),_:1})]),_:1},8,["modelValue"])]),_:1}),s.testScanForm.scanType==="range"?(u(),w(R,{key:0},[e(n,{label:"起始IP",prop:"startIp"},{default:l(()=>[e(g,{modelValue:s.testScanForm.startIp,"onUpdate:modelValue":a[19]||(a[19]=t=>s.testScanForm.startIp=t),placeholder:"请输入起始IP地址"},null,8,["modelValue"])]),_:1}),e(n,{label:"结束IP",prop:"endIp"},{default:l(()=>[e(g,{modelValue:s.testScanForm.endIp,"onUpdate:modelValue":a[20]||(a[20]=t=>s.testScanForm.endIp=t),placeholder:"请输入结束IP地址"},null,8,["modelValue"])]),_:1})],64)):(u(),f(n,{key:1,label:"CIDR网段",prop:"cidr"},{default:l(()=>[e(g,{modelValue:s.testScanForm.cidr,"onUpdate:modelValue":a[21]||(a[21]=t=>s.testScanForm.cidr=t),placeholder:"请输入CIDR格式的网段，如***********/24"},null,8,["modelValue"])]),_:1})),e(n,{label:"超时(毫秒)",prop:"timeout"},{default:l(()=>[e(G,{modelValue:s.testScanForm.timeout,"onUpdate:modelValue":a[22]||(a[22]=t=>s.testScanForm.timeout=t),min:100,max:5e3,step:100},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),s.testScanResults.length>0?(u(),w("div",ke,[be,e(C,{data:s.testScanResults,border:"",stripe:""},{default:l(()=>[e(p,{prop:"ip",label:"IP地址"}),e(p,{prop:"hostname",label:"主机名"}),e(p,{prop:"openPorts",label:"开放端口"},{default:l(t=>[o(y(t.row.openPorts.join(", ")),1)]),_:1})]),_:1},8,["data"])])):D("",!0)]),_:1},8,["modelValue"]),e(P,{modelValue:s.dialogVisible.queueStatus,"onUpdate:modelValue":a[26]||(a[26]=t=>s.dialogVisible.queueStatus=t),title:"任务队列状态",width:"800px","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[b("span",Ve,[e(h,{onClick:a[25]||(a[25]=t=>s.dialogVisible.queueStatus=!1)},{default:l(()=>[o("关闭")]),_:1}),e(h,{type:"primary",onClick:i.refreshQueueStatus},{default:l(()=>[e(V,null,{default:l(()=>[e(L)]),_:1}),o(" 刷新 ")]),_:1},8,["onClick"])])]),default:l(()=>[A((u(),w("div",null,[e(x,{title:"注意：此对话框只能通过点击关闭按钮关闭",type:"info",description:"您可以随时通过页面上的“查看队列状态”按钮再次打开此对话框","show-icon":"",closable:!1,style:{"margin-bottom":"15px"}}),e(x,{title:`系统状态: ${s.queueStatus.systemStatus==="processing"?"正在处理队列":"空闲"}`,type:s.queueStatus.systemStatus==="processing"?"warning":"success",description:`当前队列长度: ${s.queueStatus.queueLength} | 正在运行任务数: ${s.queueStatus.runningCount} | 最大并发数: ${s.queueStatus.maxConcurrent}`,"show-icon":"",closable:!1,style:{"margin-bottom":"15px"}},null,8,["title","type","description"]),b("div",we,[De,e(C,{data:s.queueStatus.runningTasks||[],border:"",stripe:"",style:{width:"100%","margin-bottom":"20px"},"empty-text":"没有正在运行的任务","max-height":"200px"},{default:l(()=>[e(p,{prop:"id",label:"任务ID",width:"80"}),e(p,{prop:"name",label:"任务名称"}),e(p,{prop:"status",label:"状态",width:"100"},{default:l(t=>[e(S,{type:"warning"},{default:l(()=>[o("运行中")]),_:1})]),_:1}),e(p,{prop:"startTime",label:"开始时间",width:"180"},{default:l(t=>[o(y(i.formatDateTime(t.row.startTime)),1)]),_:1}),e(p,{prop:"runningTime",label:"运行时间",width:"120"},{default:l(t=>[o(y(t.row.runningTime)+" 秒 ",1)]),_:1})]),_:1},8,["data"]),b("h3",null,[o("排队中的任务 "),s.queueStatus.queuedTasks&&s.queueStatus.queuedTasks.length>0?(u(),w("span",Ie,"("+y(s.queueStatus.queuedTasks.length)+"个)",1)):D("",!0)]),e(C,{data:s.queueStatus.queuedTasks||[],border:"",stripe:"",style:{width:"100%"},"empty-text":"没有排队中的任务","max-height":"300px"},{default:l(()=>[e(p,{prop:"id",label:"任务ID",width:"80"}),e(p,{prop:"name",label:"任务名称"}),e(p,{prop:"status",label:"状态",width:"100"},{default:l(t=>[e(S,{type:"info"},{default:l(()=>[o("排队中")]),_:1})]),_:1}),e(p,{prop:"addedTime",label:"添加时间",width:"180"},{default:l(t=>[o(y(i.formatDateTime(t.row.addedTime)),1)]),_:1})]),_:1},8,["data"])])])),[[Q,s.queueStatusLoading]])]),_:1},8,["modelValue"])])}const Te=Y(ae,[["render",ve],["__scopeId","data-v-6a4268ce"]]);export{Te as default};
