import{_ as T,z as Q,v as G,A as H,c,a,f as s,w as o,b as v,F as h,l as g,h as V,B as K,C as X,m as D,x as C,t as x,p as Z,q as $,e as i,W as S,n as P}from"./index-BnaD8Tdo.js";import{u as B,w as ee,F as le}from"./FileSaver.min-CkyCPK_c.js";const ae={components:{Plus:H,Search:G,Download:Q},data(){var n,l,m;return{userArr:[],loading:!1,userList:[],systemlevels:[],xinchuangcategoryminors:[],xinchuangcategorymajors:[],systemattributes:[],constructionMethods:[],technicalRoutes:[],backupStandards:[],businessLines:[],systemCategories:[],hasDeletePermission:(n=localStorage.getItem("role_code"))==null?void 0:n.includes("D"),hasUpdatePermission:(l=localStorage.getItem("role_code"))==null?void 0:l.includes("U"),hasInsertPermission:(m=localStorage.getItem("role_code"))==null?void 0:m.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{self_build_system_id:"",system_abbreviation:"",main_admin:"",system_attribute:"",system_level:"",jrt_0059_backup_standard:"",xinchuang_category_major:"",xinchuang_category_minor:"",construction_method:"",technical_route:"",is_reported_to_external:"",operation_status:"",xinchuang_status:"",security_level:"",general_function_domain:"",futures_function_domain:"",business_line:"",system_category:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,self_build_system_id:"",system_abbreviation:"",main_admin:"",backup_admin:"",business_department:"",system_attribute:"",go_live_date:"",decommission_date:"",major_milestones:"",industry_name:"",monitoring_system_name:"",system_function_summary:"",system_form:"",cs_client_name:"",bs_url:"",ip_port:"",business_line:"",system_category:"",system_level:"",has_backup_strategy:"",server_count:0,remarks:"",monitoring_coverage:"",digital_classification:"",jrt_0059_backup_standard:"",xinchuang_category_major:"",xinchuang_category_minor:"",software_copyright_name:"",construction_method:"",technical_route:"",operation_status:"",xinchuang_status:"",security_level:"",general_function_domains:[],futures_function_domains:[],is_reported_to_external:"",centos7_count:0,created_at:""},generalFunctionDomains:[],futuresFunctionDomains:[],rules:{system_abbreviation:[{required:!0,message:"请输入业务系统简称",trigger:"blur"}],main_admin:[{required:!0,message:"请选择主岗",trigger:"change"}],system_attribute:[{required:!0,message:"请选择系统属性",trigger:"change"}],industry_name:[{required:!0,message:"请输入业务系统行业名称",trigger:"blur"}],system_function_summary:[{required:!0,message:"请输入系统功能描述",trigger:"blur"}],business_line:[{required:!0,message:"请输入业务线条",trigger:"blur"}],system_category:[{required:!0,message:"请输入业务系统大类",trigger:"blur"}],system_level:[{required:!0,message:"请选择系统分级",trigger:"change"}]}}},mounted(){this.loadData(),this.getDatadict("C","systemattributes"),this.getDatadict("I","systemlevels"),this.getDatadict("J","xinchuangcategoryminors"),this.getDatadict("M","xinchuangcategorymajors"),this.getDatadict("N","constructionMethods"),this.getDatadict("O","technicalRoutes"),this.getDatadict("Q","backupStandards"),this.getDatadict("R","businessLines"),this.getDatadict("S","systemCategories"),this.getFunctionDomains("通用功能域","generalFunctionDomains"),this.getFunctionDomains("期货经营机构","futuresFunctionDomains"),this.getUserList()},methods:{handlePageChange(n){this.search.currentPage=n,this.loadData()},handlePageSizeChange(n){this.search.pageSize=parseInt(n),this.search.currentPage=1,this.loadData()},handleSortChange({prop:n,order:l}){this.search.sortProp=n,this.search.sortOrder=l==="ascending"?"asc":"desc",this.loadData()},resetSearch(){this.search={self_build_system_id:"",system_abbreviation:"",main_admin:"",system_attribute:"",system_level:"",jrt_0059_backup_standard:"",xinchuang_category_major:"",xinchuang_category_minor:"",construction_method:"",technical_route:"",is_reported_to_external:"",operation_status:"",xinchuang_status:"",security_level:"",general_function_domain:"",futures_function_domain:"",business_line:"",system_category:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},async loadData(){try{this.loading=!0;const n=await this.$axios.post("/api/get_cmdb_system_admin_responsibility_company",this.search);this.userArr=n.data.msg,this.search.total=n.data.total}catch(n){console.error("数据加载失败:",n),this.$message.error("数据加载失败")}finally{this.loading=!1}},async validateAndSubmitAdd(){try{await this.$refs.addFormRef.validate(),await this.submitAdd()}catch{this.$message.error("请完善必填项后再提交")}},async submitAdd(){try{const n={...this.formData};Array.isArray(n.general_function_domains)&&(n.general_function_domains=JSON.stringify(n.general_function_domains)),Array.isArray(n.futures_function_domains)&&(n.futures_function_domains=JSON.stringify(n.futures_function_domains)),await this.$axios.post("/api/add_cmdb_system_admin_responsibility_company",n),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(n){console.error("添加失败:",n),this.$message.error("添加失败")}},async validateAndSubmitEdit(){try{await this.$refs.editFormRef.validate(),await this.submitEdit()}catch{this.$message.error("请完善必填项后再提交")}},async submitEdit(){try{const n={...this.formData};Array.isArray(n.general_function_domains)&&(n.general_function_domains=JSON.stringify(n.general_function_domains)),Array.isArray(n.futures_function_domains)&&(n.futures_function_domains=JSON.stringify(n.futures_function_domains)),await this.$axios.post("/api/update_cmdb_system_admin_responsibility_company",n),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(n){console.error("更新失败:",n),this.$message.error("更新失败")}},async submitDelete(){try{await this.$axios.post("/api/del_cmdb_system_admin_responsibility_company",this.formData),this.$message.success("删除成功"),this.loadData(),this.dialogVisible.delete=!1}catch(n){console.error("删除失败:",n),this.$message.error("删除失败")}},async getDatadict(n,l){try{const m=await this.$axios.post("/api/get_cmdb_data_dictionary",{dict_code:n});this[l]=m.data.msg}catch(m){console.error("数据加载失败:",m),this.$message.error("数据加载失败")}},async getFunctionDomains(n,l){try{const m=await this.$axios.post("/api/get_cmdb_function_domains",{function_domain_type:n});this[l]=m.data.msg.map(y=>({value:y.function_domain_code,label:y.function_domain_name,isCore:y.is_core_domain}))}catch(m){console.error("功能域数据加载失败:",m),this.$message.error("功能域数据加载失败")}},async getUserList(){try{const n=await this.$axios.post("/api/get_all_users_real_name");this.userList=n.data.msg}catch(n){console.error("获取用户列表失败:",n),this.$message.error("获取用户列表失败")}},getUserRealName(n){if(!n)return"-";const l=this.userList.find(m=>m.username===n);return l?l.real_name:n},getBackupStandardName(n){if(!n)return"-";const l=this.backupStandards.find(m=>m.dict_code===n);return l?l.dict_name:n},getBusinessLineName(n){if(!n)return"-";const l=this.businessLines.find(m=>m.dict_code===n);return l?l.dict_name:n},getSystemCategoryName(n){if(!n)return"-";const l=this.systemCategories.find(m=>m.dict_code===n);return l?l.dict_name:n},formatFunctionDomainNames(n,l,m=!1){try{if(!n)return"-";let y=[];if(typeof n=="string")try{y=JSON.parse(n)}catch(f){if(console.warn("JSON解析失败，尝试修复格式:",f),n.trim().startsWith("[")&&n.trim().endsWith("]"))try{const p=n.replace(/'/g,'"').replace(/(\w+):/g,'"$1":');y=JSON.parse(p)}catch{y=n.replace(/[\[\]'"\s]/g,"").split(",").filter(Boolean)}else y=n.split(",").map(p=>p.trim()).filter(Boolean)}else Array.isArray(n)&&(y=n);if(Array.isArray(y)||(y=[y]),y.length===0)return"-";const t=this[l]||[],d=y.map(f=>{const p=String(f).trim(),r=t.find(_=>_.value===p);return r?r.isCore?`${r.label}(核心)`:r.label:p});return m&&d.length>2?`${d.slice(0,2).join("、")} 等${d.length}项`:d.join("、")}catch(y){return console.error("格式化功能域名称失败:",y),"格式错误"}},parseDomainDetails(n,l){try{if(!n)return[];let m=[];if(typeof n=="string")try{m=JSON.parse(n)}catch{if(n.trim().startsWith("[")&&n.trim().endsWith("]"))try{const d=n.replace(/'/g,'"').replace(/(\w+):/g,'"$1":');m=JSON.parse(d)}catch{m=n.replace(/[\[\]'"\s]/g,"").split(",").filter(Boolean)}else m=n.split(",").map(d=>d.trim()).filter(Boolean)}else Array.isArray(n)&&(m=n);if(Array.isArray(m)||(m=[m]),m.length===0)return[];const y=this[l]||[];return m.map(t=>{const d=String(t).trim(),f=y.find(p=>p.value===d);return f?{code:d,name:f.label,isCore:f.isCore}:{code:d,name:d,isCore:!1}})}catch(m){return console.error("解析功能域详情失败:",m),[]}},getDomainCount(n){try{if(!n)return 0;let l=[];if(typeof n=="string")try{l=JSON.parse(n)}catch{if(n.trim().startsWith("[")&&n.trim().endsWith("]"))try{const y=n.replace(/'/g,'"').replace(/(\w+):/g,'"$1":');l=JSON.parse(y)}catch{l=n.replace(/[\[\]'"\s]/g,"").split(",").filter(Boolean)}else l=n.split(",").map(y=>y.trim()).filter(Boolean)}else Array.isArray(n)&&(l=n);return Array.isArray(l)||(l=[l]),l.length}catch(l){return console.error("获取功能域总数失败:",l),0}},countCoreDomains(n,l){try{if(!n)return 0;let m=[];if(typeof n=="string")try{m=JSON.parse(n)}catch{if(n.trim().startsWith("[")&&n.trim().endsWith("]"))try{const d=n.replace(/'/g,'"').replace(/(\w+):/g,'"$1":');m=JSON.parse(d)}catch{m=n.replace(/[\[\]'"\s]/g,"").split(",").filter(Boolean)}else m=n.split(",").map(d=>d.trim()).filter(Boolean)}else Array.isArray(n)&&(m=n);if(Array.isArray(m)||(m=[m]),m.length===0)return 0;const y=this[l]||[];return m.reduce((t,d)=>{const f=String(d).trim(),p=y.find(r=>r.value===f);return p&&p.isCore?t+1:t},0)}catch(m){return console.error("统计核心功能域数量失败:",m),0}},handleDomainClick(n){n.stopPropagation()},handleAdd(){this.dialogVisible.add=!this.dialogVisible.add,this.formData={}},handleEdit(n,l){this.dialogVisible.edit=!0,this.formData.id=l.id,this.formData.self_build_system_id=l.self_build_system_id,this.formData.system_abbreviation=l.system_abbreviation,this.formData.main_admin=l.main_admin,this.formData.backup_admin=l.backup_admin,this.formData.business_department=l.business_department,this.formData.system_attribute=l.system_attribute,this.formData.go_live_date=l.go_live_date,this.formData.decommission_date=l.decommission_date,this.formData.major_milestones=l.major_milestones,this.formData.industry_name=l.industry_name,this.formData.monitoring_system_name=l.monitoring_system_name,this.formData.system_function_summary=l.system_function_summary,this.formData.system_form=l.system_form,this.formData.cs_client_name=l.cs_client_name,this.formData.bs_url=l.bs_url,this.formData.ip_port=l.ip_port,this.formData.business_line=l.business_line,this.formData.system_category=l.system_category,this.formData.system_level=l.system_level,this.formData.has_backup_strategy=l.has_backup_strategy,this.formData.server_count=l.server_count,this.formData.remarks=l.remarks,this.formData.monitoring_coverage=l.monitoring_coverage,this.formData.digital_classification=l.digital_classification,this.formData.jrt_0059_backup_standard=l.jrt_0059_backup_standard,this.formData.xinchuang_category_major=l.xinchuang_category_major,this.formData.xinchuang_category_minor=l.xinchuang_category_minor,this.formData.software_copyright_name=l.software_copyright_name,this.formData.construction_method=l.construction_method,this.formData.technical_route=l.technical_route,this.formData.operation_status=l.operation_status,this.formData.xinchuang_status=l.xinchuang_status,this.formData.security_level=l.security_level;try{if(l.general_function_domains)if(typeof l.general_function_domains=="string")try{this.formData.general_function_domains=JSON.parse(l.general_function_domains)}catch(m){if(console.warn("解析通用功能域失败，尝试修复格式:",m),l.general_function_domains.trim().startsWith("[")&&l.general_function_domains.trim().endsWith("]"))try{const y=l.general_function_domains.replace(/'/g,'"').replace(/(\w+):/g,'"$1":');this.formData.general_function_domains=JSON.parse(y)}catch{this.formData.general_function_domains=l.general_function_domains.replace(/[\[\]'"\s]/g,"").split(",").filter(Boolean)}else this.formData.general_function_domains=l.general_function_domains.split(",").map(y=>y.trim()).filter(Boolean)}else Array.isArray(l.general_function_domains)?this.formData.general_function_domains=l.general_function_domains:this.formData.general_function_domains=[];else this.formData.general_function_domains=[]}catch(m){console.error("处理通用功能域失败:",m),this.formData.general_function_domains=[]}try{if(l.futures_function_domains)if(typeof l.futures_function_domains=="string")try{this.formData.futures_function_domains=JSON.parse(l.futures_function_domains)}catch(m){if(console.warn("解析经营机构功能域失败，尝试修复格式:",m),l.futures_function_domains.trim().startsWith("[")&&l.futures_function_domains.trim().endsWith("]"))try{const y=l.futures_function_domains.replace(/'/g,'"').replace(/(\w+):/g,'"$1":');this.formData.futures_function_domains=JSON.parse(y)}catch{this.formData.futures_function_domains=l.futures_function_domains.replace(/[\[\]'"\s]/g,"").split(",").filter(Boolean)}else this.formData.futures_function_domains=l.futures_function_domains.split(",").map(y=>y.trim()).filter(Boolean)}else Array.isArray(l.futures_function_domains)?this.formData.futures_function_domains=l.futures_function_domains:this.formData.futures_function_domains=[];else this.formData.futures_function_domains=[]}catch(m){console.error("处理经营机构功能域失败:",m),this.formData.futures_function_domains=[]}this.formData.is_reported_to_external=l.is_reported_to_external,this.formData.centos7_count=l.centos7_count,this.formData.created_at=l.created_at},handleDelete(n,l){this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=l.id,this.formData.self_build_system_id=l.self_build_system_id},exportData(){const l=this.$refs.table.columns,m=l.map(_=>_.label),y=this.userArr.map(_=>l.map(A=>_[A.property])),t=[m,...y],d=B.aoa_to_sheet(t),f=B.book_new();B.book_append_sheet(f,d,"Sheet1");const p=ee(f,{bookType:"xlsx",type:"array"}),r=new Blob([p],{type:"application/octet-stream"});le.saveAs(r,"系统管理员责任表-公司管理.xlsx")}}},u=n=>(Z("data-v-44f3bdd7"),n=n(),$(),n),te={class:"user-manage"},se={class:"dialogdiv"},oe=u(()=>s("span",{class:"label"},"自建系统编号:",-1)),ne=u(()=>s("span",{class:"label required-label"},"业务系统简称:",-1)),re=u(()=>s("span",{class:"label required-label"},"主岗:",-1)),ie=u(()=>s("span",{class:"label"},"备岗:",-1)),ue=u(()=>s("span",{class:"label"},"业务主管部门:",-1)),de=u(()=>s("span",{class:"label required-label"},"系统属性:",-1)),me=u(()=>s("span",{class:"label"},"上线时间:",-1)),_e=u(()=>s("span",{class:"label"},"下线时间:",-1)),ce=u(()=>s("span",{class:"label"},"重大历程:",-1)),pe=u(()=>s("span",{class:"label required-label"},"业务系统行业名称:",-1)),fe=u(()=>s("span",{class:"label"},"业务系统英文简称:",-1)),be=u(()=>s("span",{class:"label required-label"},"系统功能简述:",-1)),ye=u(()=>s("span",{class:"label"},"系统形态:",-1)),he=u(()=>s("span",{class:"label"},"CS客户端程序名称:",-1)),ge=u(()=>s("span",{class:"label"},"BS URL地址:",-1)),De=u(()=>s("span",{class:"label"},"IP:端口:",-1)),Ve=u(()=>s("span",{class:"label required-label"},"业务线条:",-1)),ve=u(()=>s("span",{class:"label required-label"},"业务系统大类:",-1)),xe=u(()=>s("span",{class:"label required-label"},"系统分级:",-1)),ke=u(()=>s("span",{class:"label"},"是否建立备份策略:",-1)),we=u(()=>s("span",{class:"label"},"数字化分类:",-1)),Ue=u(()=>s("span",{class:"label"},"JR/T 0059-2010备份能力标准:",-1)),Ce=u(()=>s("span",{class:"label"},"系统分类:",-1)),Ae=u(()=>s("span",{class:"label"},"信创小类:",-1)),Se=u(()=>s("span",{class:"label"},"软著名称:",-1)),je=u(()=>s("span",{class:"label"},"建设方式:",-1)),Fe=u(()=>s("span",{class:"label"},"技术路线:",-1)),Ne=u(()=>s("span",{class:"label"},"运行状态:",-1)),Ye=u(()=>s("span",{class:"label"},"信创状态:",-1)),Be=u(()=>s("span",{class:"label"},"等保等级:",-1)),qe=u(()=>s("span",{class:"label"},"通用功能域:",-1)),Oe={key:0,style:{color:"red","margin-left":"5px"}},Pe=u(()=>s("span",{class:"label"},"经营机构功能域:",-1)),Le={key:0,style:{color:"red","margin-left":"5px"}},Je=u(()=>s("span",{class:"label"},"是否外部上报:",-1)),Me=u(()=>s("span",{class:"label"},"备注:",-1)),Re={class:"dialog-footer"},ze={class:"dialogdiv"},Ie=u(()=>s("span",{class:"label"},"自建系统编号:",-1)),We=u(()=>s("span",{class:"label required-label"},"业务系统简称:",-1)),Ee=u(()=>s("span",{class:"label required-label"},"主岗:",-1)),Te=u(()=>s("span",{class:"label"},"备岗:",-1)),Qe=u(()=>s("span",{class:"label"},"业务主管部门:",-1)),Ge=u(()=>s("span",{class:"label required-label"},"系统属性:",-1)),He=u(()=>s("span",{class:"label"},"上线时间:",-1)),Ke=u(()=>s("span",{class:"label"},"下线时间:",-1)),Xe=u(()=>s("span",{class:"label"},"重大历程:",-1)),Ze=u(()=>s("span",{class:"label required-label"},"业务系统行业名称:",-1)),$e=u(()=>s("span",{class:"label"},"业务系统英文简称:",-1)),el=u(()=>s("span",{class:"label required-label"},"系统功能简述:",-1)),ll=u(()=>s("span",{class:"label"},"系统形态:",-1)),al=u(()=>s("span",{class:"label"},"CS客户端程序名称:",-1)),tl=u(()=>s("span",{class:"label"},"BS URL地址:",-1)),sl=u(()=>s("span",{class:"label"},"IP:端口:",-1)),ol=u(()=>s("span",{class:"label required-label"},"业务线条:",-1)),nl=u(()=>s("span",{class:"label required-label"},"业务系统大类:",-1)),rl=u(()=>s("span",{class:"label required-label"},"系统分级:",-1)),il=u(()=>s("span",{class:"label"},"是否建立备份策略:",-1)),ul=u(()=>s("span",{class:"label"},"数字化分类:",-1)),dl=u(()=>s("span",{class:"label"},"JR/T 0059-2010备份能力标准:",-1)),ml=u(()=>s("span",{class:"label"},"系统分类:",-1)),_l=u(()=>s("span",{class:"label"},"信创小类:",-1)),cl=u(()=>s("span",{class:"label"},"软著名称:",-1)),pl=u(()=>s("span",{class:"label"},"建设方式:",-1)),fl=u(()=>s("span",{class:"label"},"技术路线:",-1)),bl=u(()=>s("span",{class:"label"},"运行状态:",-1)),yl=u(()=>s("span",{class:"label"},"信创状态:",-1)),hl=u(()=>s("span",{class:"label"},"等保等级:",-1)),gl=u(()=>s("span",{class:"label"},"通用功能域:",-1)),Dl={key:0,style:{color:"red","margin-left":"5px"}},Vl=u(()=>s("span",{class:"label"},"经营机构功能域:",-1)),vl={key:0,style:{color:"red","margin-left":"5px"}},xl=u(()=>s("span",{class:"label"},"是否外部上报:",-1)),kl=u(()=>s("span",{class:"label"},"备注:",-1)),wl={class:"dialog-footer"},Ul=u(()=>s("p",{class:"description",style:{"line-height":"1.5","margin-bottom":"10px",color:"#E6A23C","background-color":"#FDF6EC",padding:"8px 12px","border-radius":"4px","border-left":"4px solid #E6A23C"}}," 一级为必须监控，实时交易；二级为必须监控，非实时交易、归档备份；三级为非必须监控，生产交易终端、办公、服务类；四级非必须监控，测试、仿真、已下线 ",-1)),Cl={class:"button-container"},Al={class:"action-bar unified-action-bar"},Sl={class:"action-bar-left"},jl={class:"action-bar-right"},Fl={key:0},Nl={key:0,class:"core-count"},Yl={class:"domain-detail"},Bl=u(()=>s("h4",null,"通用功能域详情",-1)),ql={class:"domain-list"},Ol={key:0,class:"core-tag"},Pl={class:"domain-summary"},Ll={key:0},Jl={key:1},Ml={key:0},Rl={key:0,class:"core-count"},zl={class:"domain-detail"},Il=u(()=>s("h4",null,"经营机构功能域详情",-1)),Wl={class:"domain-list"},El={key:0,class:"core-tag"},Tl={class:"domain-summary"},Ql={key:0},Gl={key:1},Hl={style:{display:"flex","white-space":"nowrap"}},Kl={class:"pagination"};function Xl(n,l,m,y,t,d){const f=v("el-input"),p=v("el-form-item"),r=v("el-option"),_=v("el-select"),A=v("el-date-picker"),j=v("el-form"),U=v("el-button"),F=v("el-dialog"),L=v("el-alert"),k=v("el-col"),J=v("Search"),N=v("el-icon"),M=v("el-row"),q=v("el-card"),R=v("Plus"),z=v("Download"),b=v("el-table-column"),O=v("el-popover"),I=v("el-table"),W=v("el-pagination"),E=X("loading");return i(),c("div",te,[a(F,{modelValue:t.dialogVisible.add,"onUpdate:modelValue":l[35]||(l[35]=e=>t.dialogVisible.add=e),title:"新增系统信息",width:"450","align-center":""},{footer:o(()=>[s("div",Re,[a(U,{onClick:l[34]||(l[34]=e=>t.dialogVisible.add=!1)},{default:o(()=>[V("返回")]),_:1}),a(U,{type:"primary",onClick:d.validateAndSubmitAdd},{default:o(()=>[V("确定")]),_:1},8,["onClick"])])]),default:o(()=>[s("div",se,[a(j,{model:t.formData,rules:t.rules,ref:"addFormRef","label-position":"right","status-icon":""},{default:o(()=>[s("p",null,[oe,a(f,{modelValue:t.formData.self_build_system_id,"onUpdate:modelValue":l[0]||(l[0]=e=>t.formData.self_build_system_id=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[a(p,{prop:"system_abbreviation",class:"inline-form-item"},{default:o(()=>[ne,a(f,{modelValue:t.formData.system_abbreviation,"onUpdate:modelValue":l[1]||(l[1]=e=>t.formData.system_abbreviation=e),style:{width:"240px"},clearable:"",placeholder:"请输入业务系统简称"},null,8,["modelValue"])]),_:1})]),s("p",null,[a(p,{prop:"main_admin",class:"inline-form-item"},{default:o(()=>[re,a(_,{modelValue:t.formData.main_admin,"onUpdate:modelValue":l[2]||(l[2]=e=>t.formData.main_admin=e),style:{width:"240px"},filterable:"",placeholder:"请选择主岗"},{default:o(()=>[(i(!0),c(h,null,g(t.userList,e=>(i(),D(r,{key:e.username,label:e.real_name,value:e.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),s("p",null,[ie,a(_,{modelValue:t.formData.backup_admin,"onUpdate:modelValue":l[3]||(l[3]=e=>t.formData.backup_admin=e),style:{width:"240px"},filterable:"",placeholder:"请选择备岗"},{default:o(()=>[(i(!0),c(h,null,g(t.userList,e=>(i(),D(r,{key:e.username,label:e.real_name,value:e.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[ue,a(f,{modelValue:t.formData.business_department,"onUpdate:modelValue":l[4]||(l[4]=e=>t.formData.business_department=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[a(p,{prop:"system_attribute",class:"inline-form-item"},{default:o(()=>[de,a(_,{modelValue:t.formData.system_attribute,"onUpdate:modelValue":l[5]||(l[5]=e=>t.formData.system_attribute=e),style:{width:"240px"},placeholder:"请选择系统属性"},{default:o(()=>[(i(!0),c(h,null,g(t.systemattributes,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),s("p",null,[me,a(A,{modelValue:t.formData.go_live_date,"onUpdate:modelValue":l[6]||(l[6]=e=>t.formData.go_live_date=e),type:"date",placeholder:"选择日期",style:{width:"240px"},"value-format":"YYYY-MM-DD",format:"YYYY-MM-DD"},null,8,["modelValue"])]),s("p",null,[_e,a(A,{modelValue:t.formData.decommission_date,"onUpdate:modelValue":l[7]||(l[7]=e=>t.formData.decommission_date=e),type:"date",placeholder:"选择日期",style:{width:"240px"},"value-format":"YYYY-MM-DD",format:"YYYY-MM-DD"},null,8,["modelValue"])]),s("p",null,[ce,a(f,{type:"textarea",rows:2,modelValue:t.formData.major_milestones,"onUpdate:modelValue":l[8]||(l[8]=e=>t.formData.major_milestones=e),style:{width:"240px"}},null,8,["modelValue"])]),s("p",null,[a(p,{prop:"industry_name",class:"inline-form-item"},{default:o(()=>[pe,a(f,{modelValue:t.formData.industry_name,"onUpdate:modelValue":l[9]||(l[9]=e=>t.formData.industry_name=e),style:{width:"240px"},clearable:"",placeholder:"请输入业务系统行业名称"},null,8,["modelValue"])]),_:1})]),s("p",null,[fe,a(f,{modelValue:t.formData.monitoring_system_name,"onUpdate:modelValue":l[10]||(l[10]=e=>t.formData.monitoring_system_name=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[a(p,{prop:"system_function_summary",class:"inline-form-item"},{default:o(()=>[be,a(f,{type:"textarea",rows:2,modelValue:t.formData.system_function_summary,"onUpdate:modelValue":l[11]||(l[11]=e=>t.formData.system_function_summary=e),style:{width:"240px"},placeholder:"请输入系统功能简述"},null,8,["modelValue"])]),_:1})]),s("p",null,[ye,a(f,{modelValue:t.formData.system_form,"onUpdate:modelValue":l[12]||(l[12]=e=>t.formData.system_form=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[he,a(f,{modelValue:t.formData.cs_client_name,"onUpdate:modelValue":l[13]||(l[13]=e=>t.formData.cs_client_name=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[ge,a(f,{modelValue:t.formData.bs_url,"onUpdate:modelValue":l[14]||(l[14]=e=>t.formData.bs_url=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[De,a(f,{modelValue:t.formData.ip_port,"onUpdate:modelValue":l[15]||(l[15]=e=>t.formData.ip_port=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[a(p,{prop:"business_line",class:"inline-form-item"},{default:o(()=>[Ve,a(_,{modelValue:t.formData.business_line,"onUpdate:modelValue":l[16]||(l[16]=e=>t.formData.business_line=e),style:{width:"240px"},placeholder:"请选择业务线条",clearable:""},{default:o(()=>[(i(!0),c(h,null,g(t.businessLines,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),s("p",null,[a(p,{prop:"system_category",class:"inline-form-item"},{default:o(()=>[ve,a(_,{modelValue:t.formData.system_category,"onUpdate:modelValue":l[17]||(l[17]=e=>t.formData.system_category=e),style:{width:"240px"},placeholder:"请选择业务系统大类",clearable:""},{default:o(()=>[(i(!0),c(h,null,g(t.systemCategories,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),s("p",null,[a(p,{prop:"system_level",class:"inline-form-item"},{default:o(()=>[xe,a(_,{modelValue:t.formData.system_level,"onUpdate:modelValue":l[18]||(l[18]=e=>t.formData.system_level=e),style:{width:"240px"},placeholder:"请选择系统分级"},{default:o(()=>[(i(!0),c(h,null,g(t.systemlevels,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),s("p",null,[ke,a(_,{modelValue:t.formData.has_backup_strategy,"onUpdate:modelValue":l[19]||(l[19]=e=>t.formData.has_backup_strategy=e),style:{width:"240px"}},{default:o(()=>[a(r,{label:"是",value:"是"}),a(r,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),s("p",null,[we,a(f,{modelValue:t.formData.digital_classification,"onUpdate:modelValue":l[20]||(l[20]=e=>t.formData.digital_classification=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[Ue,a(_,{modelValue:t.formData.jrt_0059_backup_standard,"onUpdate:modelValue":l[21]||(l[21]=e=>t.formData.jrt_0059_backup_standard=e),style:{width:"240px"},placeholder:"请选择备份能力标准",clearable:""},{default:o(()=>[(i(!0),c(h,null,g(t.backupStandards,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[Ce,a(_,{modelValue:t.formData.xinchuang_category_major,"onUpdate:modelValue":l[22]||(l[22]=e=>t.formData.xinchuang_category_major=e),style:{width:"240px"},placeholder:"请选择系统分类"},{default:o(()=>[(i(!0),c(h,null,g(t.xinchuangcategorymajors,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[Ae,a(_,{modelValue:t.formData.xinchuang_category_minor,"onUpdate:modelValue":l[23]||(l[23]=e=>t.formData.xinchuang_category_minor=e),style:{width:"240px"},placeholder:"请选择信创小类"},{default:o(()=>[(i(!0),c(h,null,g(t.xinchuangcategoryminors,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[Se,a(f,{modelValue:t.formData.software_copyright_name,"onUpdate:modelValue":l[24]||(l[24]=e=>t.formData.software_copyright_name=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[je,a(_,{modelValue:t.formData.construction_method,"onUpdate:modelValue":l[25]||(l[25]=e=>t.formData.construction_method=e),style:{width:"240px"},placeholder:"请选择建设方式"},{default:o(()=>[(i(!0),c(h,null,g(t.constructionMethods,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[Fe,a(_,{modelValue:t.formData.technical_route,"onUpdate:modelValue":l[26]||(l[26]=e=>t.formData.technical_route=e),style:{width:"240px"},placeholder:"请选择技术路线"},{default:o(()=>[(i(!0),c(h,null,g(t.technicalRoutes,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[Ne,a(_,{modelValue:t.formData.operation_status,"onUpdate:modelValue":l[27]||(l[27]=e=>t.formData.operation_status=e),style:{width:"240px"},placeholder:"请选择运行状态"},{default:o(()=>[a(r,{label:"建设中",value:"建设中"}),a(r,{label:"运行中",value:"运行中"}),a(r,{label:"已下线",value:"已下线"})]),_:1},8,["modelValue"])]),s("p",null,[Ye,a(_,{modelValue:t.formData.xinchuang_status,"onUpdate:modelValue":l[28]||(l[28]=e=>t.formData.xinchuang_status=e),style:{width:"240px"},placeholder:"请选择信创状态"},{default:o(()=>[a(r,{label:"未信创",value:"未信创"}),a(r,{label:"完成开发或测试",value:"完成开发或测试"}),a(r,{label:"非全栈双轨",value:"非全栈双轨"}),a(r,{label:"非全栈单轨",value:"非全栈单轨"}),a(r,{label:"全栈双轨",value:"全栈双轨"}),a(r,{label:"全栈单轨",value:"全栈单轨"})]),_:1},8,["modelValue"])]),s("p",null,[Be,a(_,{modelValue:t.formData.security_level,"onUpdate:modelValue":l[29]||(l[29]=e=>t.formData.security_level=e),style:{width:"240px"},placeholder:"请选择等保等级"},{default:o(()=>[a(r,{label:"一级",value:"一级"}),a(r,{label:"二级",value:"二级"}),a(r,{label:"三级",value:"三级"})]),_:1},8,["modelValue"])]),s("p",null,[qe,a(_,{modelValue:t.formData.general_function_domains,"onUpdate:modelValue":l[30]||(l[30]=e=>t.formData.general_function_domains=e),multiple:"","collapse-tags":"",placeholder:"请选择通用功能域",style:{width:"240px"}},{default:o(()=>[(i(!0),c(h,null,g(t.generalFunctionDomains,e=>(i(),D(r,{key:e.value,label:e.label,value:e.value},{default:o(()=>[s("span",{style:S({color:e.isCore?"red":"inherit"})},x(e.label),5),e.isCore?(i(),c("span",Oe,"(核心)")):C("",!0)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[Pe,a(_,{modelValue:t.formData.futures_function_domains,"onUpdate:modelValue":l[31]||(l[31]=e=>t.formData.futures_function_domains=e),multiple:"","collapse-tags":"",placeholder:"请选择经营机构功能域",style:{width:"240px"}},{default:o(()=>[(i(!0),c(h,null,g(t.futuresFunctionDomains,e=>(i(),D(r,{key:e.value,label:e.label,value:e.value},{default:o(()=>[s("span",{style:S({color:e.isCore?"red":"inherit"})},x(e.label),5),e.isCore?(i(),c("span",Le,"(核心)")):C("",!0)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[Je,a(_,{modelValue:t.formData.is_reported_to_external,"onUpdate:modelValue":l[32]||(l[32]=e=>t.formData.is_reported_to_external=e),style:{width:"240px"}},{default:o(()=>[a(r,{label:"是",value:"是"}),a(r,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),s("p",null,[Me,a(f,{type:"textarea",rows:2,modelValue:t.formData.remarks,"onUpdate:modelValue":l[33]||(l[33]=e=>t.formData.remarks=e),style:{width:"240px"}},null,8,["modelValue"])])]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),a(F,{modelValue:t.dialogVisible.edit,"onUpdate:modelValue":l[71]||(l[71]=e=>t.dialogVisible.edit=e),title:"编辑系统信息",width:"450","align-center":""},{footer:o(()=>[s("div",wl,[a(U,{onClick:l[70]||(l[70]=e=>t.dialogVisible.edit=!1)},{default:o(()=>[V("取消")]),_:1}),a(U,{type:"primary",onClick:d.validateAndSubmitEdit},{default:o(()=>[V("更新")]),_:1},8,["onClick"])])]),default:o(()=>[s("div",ze,[a(j,{model:t.formData,rules:t.rules,ref:"editFormRef","label-position":"right","status-icon":""},{default:o(()=>[s("p",null,[Ie,a(f,{modelValue:t.formData.self_build_system_id,"onUpdate:modelValue":l[36]||(l[36]=e=>t.formData.self_build_system_id=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[a(p,{prop:"system_abbreviation",class:"inline-form-item"},{default:o(()=>[We,a(f,{modelValue:t.formData.system_abbreviation,"onUpdate:modelValue":l[37]||(l[37]=e=>t.formData.system_abbreviation=e),style:{width:"240px"},clearable:"",placeholder:"请输入业务系统简称"},null,8,["modelValue"])]),_:1})]),s("p",null,[a(p,{prop:"main_admin",class:"inline-form-item"},{default:o(()=>[Ee,a(_,{modelValue:t.formData.main_admin,"onUpdate:modelValue":l[38]||(l[38]=e=>t.formData.main_admin=e),style:{width:"240px"},filterable:"",placeholder:"请选择主岗"},{default:o(()=>[(i(!0),c(h,null,g(t.userList,e=>(i(),D(r,{key:e.username,label:e.real_name,value:e.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),s("p",null,[Te,a(_,{modelValue:t.formData.backup_admin,"onUpdate:modelValue":l[39]||(l[39]=e=>t.formData.backup_admin=e),style:{width:"240px"},filterable:"",placeholder:"请选择备岗"},{default:o(()=>[(i(!0),c(h,null,g(t.userList,e=>(i(),D(r,{key:e.username,label:e.real_name,value:e.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[Qe,a(f,{modelValue:t.formData.business_department,"onUpdate:modelValue":l[40]||(l[40]=e=>t.formData.business_department=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[a(p,{prop:"system_attribute",class:"inline-form-item"},{default:o(()=>[Ge,a(_,{modelValue:t.formData.system_attribute,"onUpdate:modelValue":l[41]||(l[41]=e=>t.formData.system_attribute=e),style:{width:"240px"},placeholder:"请选择系统属性"},{default:o(()=>[(i(!0),c(h,null,g(t.systemattributes,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),s("p",null,[He,a(A,{modelValue:t.formData.go_live_date,"onUpdate:modelValue":l[42]||(l[42]=e=>t.formData.go_live_date=e),type:"date",placeholder:"选择日期",style:{width:"240px"},"value-format":"YYYY-MM-DD",format:"YYYY-MM-DD"},null,8,["modelValue"])]),s("p",null,[Ke,a(A,{modelValue:t.formData.decommission_date,"onUpdate:modelValue":l[43]||(l[43]=e=>t.formData.decommission_date=e),type:"date",placeholder:"选择日期",style:{width:"240px"},"value-format":"YYYY-MM-DD",format:"YYYY-MM-DD"},null,8,["modelValue"])]),s("p",null,[Xe,a(f,{type:"textarea",rows:2,modelValue:t.formData.major_milestones,"onUpdate:modelValue":l[44]||(l[44]=e=>t.formData.major_milestones=e),style:{width:"240px"}},null,8,["modelValue"])]),s("p",null,[a(p,{prop:"industry_name",class:"inline-form-item"},{default:o(()=>[Ze,a(f,{modelValue:t.formData.industry_name,"onUpdate:modelValue":l[45]||(l[45]=e=>t.formData.industry_name=e),style:{width:"240px"},clearable:"",placeholder:"请输入业务系统行业名称"},null,8,["modelValue"])]),_:1})]),s("p",null,[$e,a(f,{modelValue:t.formData.monitoring_system_name,"onUpdate:modelValue":l[46]||(l[46]=e=>t.formData.monitoring_system_name=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[a(p,{prop:"system_function_summary",class:"inline-form-item"},{default:o(()=>[el,a(f,{type:"textarea",rows:2,modelValue:t.formData.system_function_summary,"onUpdate:modelValue":l[47]||(l[47]=e=>t.formData.system_function_summary=e),style:{width:"240px"},placeholder:"请输入系统功能简述"},null,8,["modelValue"])]),_:1})]),s("p",null,[ll,a(f,{modelValue:t.formData.system_form,"onUpdate:modelValue":l[48]||(l[48]=e=>t.formData.system_form=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[al,a(f,{modelValue:t.formData.cs_client_name,"onUpdate:modelValue":l[49]||(l[49]=e=>t.formData.cs_client_name=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[tl,a(f,{modelValue:t.formData.bs_url,"onUpdate:modelValue":l[50]||(l[50]=e=>t.formData.bs_url=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[sl,a(f,{modelValue:t.formData.ip_port,"onUpdate:modelValue":l[51]||(l[51]=e=>t.formData.ip_port=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[a(p,{prop:"business_line",class:"inline-form-item"},{default:o(()=>[ol,a(_,{modelValue:t.formData.business_line,"onUpdate:modelValue":l[52]||(l[52]=e=>t.formData.business_line=e),style:{width:"240px"},placeholder:"请选择业务线条",clearable:""},{default:o(()=>[(i(!0),c(h,null,g(t.businessLines,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),s("p",null,[a(p,{prop:"system_category",class:"inline-form-item"},{default:o(()=>[nl,a(_,{modelValue:t.formData.system_category,"onUpdate:modelValue":l[53]||(l[53]=e=>t.formData.system_category=e),style:{width:"240px"},placeholder:"请选择业务系统大类",clearable:""},{default:o(()=>[(i(!0),c(h,null,g(t.systemCategories,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),s("p",null,[a(p,{prop:"system_level",class:"inline-form-item"},{default:o(()=>[rl,a(_,{modelValue:t.formData.system_level,"onUpdate:modelValue":l[54]||(l[54]=e=>t.formData.system_level=e),style:{width:"240px"},placeholder:"请选择系统分级"},{default:o(()=>[(i(!0),c(h,null,g(t.systemlevels,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),s("p",null,[il,a(_,{modelValue:t.formData.has_backup_strategy,"onUpdate:modelValue":l[55]||(l[55]=e=>t.formData.has_backup_strategy=e),style:{width:"240px"}},{default:o(()=>[a(r,{label:"是",value:"是"}),a(r,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),s("p",null,[ul,a(f,{modelValue:t.formData.digital_classification,"onUpdate:modelValue":l[56]||(l[56]=e=>t.formData.digital_classification=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[dl,a(_,{modelValue:t.formData.jrt_0059_backup_standard,"onUpdate:modelValue":l[57]||(l[57]=e=>t.formData.jrt_0059_backup_standard=e),style:{width:"240px"},placeholder:"请选择备份能力标准",clearable:""},{default:o(()=>[(i(!0),c(h,null,g(t.backupStandards,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[ml,a(_,{modelValue:t.formData.xinchuang_category_major,"onUpdate:modelValue":l[58]||(l[58]=e=>t.formData.xinchuang_category_major=e),style:{width:"240px"},placeholder:"请选择系统分类"},{default:o(()=>[(i(!0),c(h,null,g(t.xinchuangcategorymajors,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[_l,a(_,{modelValue:t.formData.xinchuang_category_minor,"onUpdate:modelValue":l[59]||(l[59]=e=>t.formData.xinchuang_category_minor=e),style:{width:"240px"},placeholder:"请选择信创小类"},{default:o(()=>[(i(!0),c(h,null,g(t.xinchuangcategoryminors,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[cl,a(f,{modelValue:t.formData.software_copyright_name,"onUpdate:modelValue":l[60]||(l[60]=e=>t.formData.software_copyright_name=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[pl,a(_,{modelValue:t.formData.construction_method,"onUpdate:modelValue":l[61]||(l[61]=e=>t.formData.construction_method=e),style:{width:"240px"},placeholder:"请选择建设方式"},{default:o(()=>[(i(!0),c(h,null,g(t.constructionMethods,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[fl,a(_,{modelValue:t.formData.technical_route,"onUpdate:modelValue":l[62]||(l[62]=e=>t.formData.technical_route=e),style:{width:"240px"},placeholder:"请选择技术路线"},{default:o(()=>[(i(!0),c(h,null,g(t.technicalRoutes,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[bl,a(_,{modelValue:t.formData.operation_status,"onUpdate:modelValue":l[63]||(l[63]=e=>t.formData.operation_status=e),style:{width:"240px"},placeholder:"请选择运行状态"},{default:o(()=>[a(r,{label:"建设中",value:"建设中"}),a(r,{label:"运行中",value:"运行中"}),a(r,{label:"已下线",value:"已下线"})]),_:1},8,["modelValue"])]),s("p",null,[yl,a(_,{modelValue:t.formData.xinchuang_status,"onUpdate:modelValue":l[64]||(l[64]=e=>t.formData.xinchuang_status=e),style:{width:"240px"},placeholder:"请选择信创状态"},{default:o(()=>[a(r,{label:"未信创",value:"未信创"}),a(r,{label:"完成开发或测试",value:"完成开发或测试"}),a(r,{label:"非全栈双轨",value:"非全栈双轨"}),a(r,{label:"非全栈单轨",value:"非全栈单轨"}),a(r,{label:"全栈双轨",value:"全栈双轨"}),a(r,{label:"全栈单轨",value:"全栈单轨"})]),_:1},8,["modelValue"])]),s("p",null,[hl,a(_,{modelValue:t.formData.security_level,"onUpdate:modelValue":l[65]||(l[65]=e=>t.formData.security_level=e),style:{width:"240px"},placeholder:"请选择等保等级"},{default:o(()=>[a(r,{label:"一级",value:"一级"}),a(r,{label:"二级",value:"二级"}),a(r,{label:"三级",value:"三级"})]),_:1},8,["modelValue"])]),s("p",null,[gl,a(_,{modelValue:t.formData.general_function_domains,"onUpdate:modelValue":l[66]||(l[66]=e=>t.formData.general_function_domains=e),multiple:"","collapse-tags":"",style:{width:"240px"},placeholder:"请选择通用功能域"},{default:o(()=>[(i(!0),c(h,null,g(t.generalFunctionDomains,e=>(i(),D(r,{key:e.value,label:e.label,value:e.value},{default:o(()=>[s("span",{style:S({color:e.isCore?"red":"inherit"})},x(e.label),5),e.isCore?(i(),c("span",Dl,"(核心)")):C("",!0)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[Vl,a(_,{modelValue:t.formData.futures_function_domains,"onUpdate:modelValue":l[67]||(l[67]=e=>t.formData.futures_function_domains=e),multiple:"","collapse-tags":"",style:{width:"240px"},placeholder:"请选择经营机构功能域"},{default:o(()=>[(i(!0),c(h,null,g(t.futuresFunctionDomains,e=>(i(),D(r,{key:e.value,label:e.label,value:e.value},{default:o(()=>[s("span",{style:S({color:e.isCore?"red":"inherit"})},x(e.label),5),e.isCore?(i(),c("span",vl,"(核心)")):C("",!0)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[xl,a(_,{modelValue:t.formData.is_reported_to_external,"onUpdate:modelValue":l[68]||(l[68]=e=>t.formData.is_reported_to_external=e),style:{width:"240px"}},{default:o(()=>[a(r,{label:"是",value:"是"}),a(r,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),s("p",null,[kl,a(f,{type:"textarea",rows:2,modelValue:t.formData.remarks,"onUpdate:modelValue":l[69]||(l[69]=e=>t.formData.remarks=e),style:{width:"240px"}},null,8,["modelValue"])])]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),a(F,{modelValue:t.dialogVisible.delete,"onUpdate:modelValue":l[73]||(l[73]=e=>t.dialogVisible.delete=e),title:"删除自建系统编号",width:"500","align-center":""},{footer:o(()=>[s("div",null,[a(U,{onClick:l[72]||(l[72]=e=>t.dialogVisible.delete=!1)},{default:o(()=>[V("取消")]),_:1}),a(U,{type:"danger",onClick:d.submitDelete},{default:o(()=>[V("确认删除")]),_:1},8,["onClick"])])]),default:o(()=>[a(L,{type:"warning",title:`确定要删除 自建系统编号 为 ${t.formData.self_build_system_id} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),Ul,a(q,{class:"search-card"},{default:o(()=>[a(j,{inline:!0},{default:o(()=>[a(M,{gutter:10},{default:o(()=>[a(k,{span:6},{default:o(()=>[a(p,{label:"自建系统编号"},{default:o(()=>[a(f,{modelValue:t.search.self_build_system_id,"onUpdate:modelValue":l[74]||(l[74]=e=>t.search.self_build_system_id=e),placeholder:"请输入自建系统编号",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),a(k,{span:6},{default:o(()=>[a(p,{label:"业务系统简称"},{default:o(()=>[a(f,{modelValue:t.search.system_abbreviation,"onUpdate:modelValue":l[75]||(l[75]=e=>t.search.system_abbreviation=e),placeholder:"请输入业务系统简称",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),a(k,{span:6},{default:o(()=>[a(p,{label:"主岗"},{default:o(()=>[a(_,{modelValue:t.search.main_admin,"onUpdate:modelValue":l[76]||(l[76]=e=>t.search.main_admin=e),placeholder:"请选择主岗",filterable:"",clearable:"",class:"form-control"},{default:o(()=>[a(r,{label:"所有",value:""}),(i(!0),c(h,null,g(t.userList,e=>(i(),D(r,{key:e.username,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(k,{span:6},{default:o(()=>[a(p,{label:"系统属性"},{default:o(()=>[a(_,{modelValue:t.search.system_attribute,"onUpdate:modelValue":l[77]||(l[77]=e=>t.search.system_attribute=e),placeholder:"请选择系统属性",class:"form-control"},{default:o(()=>[a(r,{label:"所有",value:""}),(i(!0),c(h,null,g(t.systemattributes,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(k,{span:6},{default:o(()=>[a(p,{label:"系统分级"},{default:o(()=>[a(_,{modelValue:t.search.system_level,"onUpdate:modelValue":l[78]||(l[78]=e=>t.search.system_level=e),placeholder:"请选择系统分级",class:"form-control"},{default:o(()=>[a(r,{label:"所有",value:""}),(i(!0),c(h,null,g(t.systemlevels,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(k,{span:6},{default:o(()=>[a(p,{label:"JR/T 0059-2010"},{default:o(()=>[a(_,{modelValue:t.search.jrt_0059_backup_standard,"onUpdate:modelValue":l[79]||(l[79]=e=>t.search.jrt_0059_backup_standard=e),placeholder:"请选择备份能力标准",clearable:"",class:"form-control"},{default:o(()=>[a(r,{label:"所有",value:""}),(i(!0),c(h,null,g(t.backupStandards,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(k,{span:6},{default:o(()=>[a(p,{label:"系统分类"},{default:o(()=>[a(_,{modelValue:t.search.xinchuang_category_major,"onUpdate:modelValue":l[80]||(l[80]=e=>t.search.xinchuang_category_major=e),placeholder:"请选择系统分类",class:"form-control"},{default:o(()=>[a(r,{label:"所有",value:""}),(i(!0),c(h,null,g(t.xinchuangcategorymajors,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(k,{span:6},{default:o(()=>[a(p,{label:"信创小类"},{default:o(()=>[a(_,{modelValue:t.search.xinchuang_category_minor,"onUpdate:modelValue":l[81]||(l[81]=e=>t.search.xinchuang_category_minor=e),placeholder:"请选择信创小类",class:"form-control"},{default:o(()=>[a(r,{label:"所有",value:""}),(i(!0),c(h,null,g(t.xinchuangcategoryminors,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(k,{span:6},{default:o(()=>[a(p,{label:"是否外部上报"},{default:o(()=>[a(_,{modelValue:t.search.is_reported_to_external,"onUpdate:modelValue":l[82]||(l[82]=e=>t.search.is_reported_to_external=e),placeholder:"请选择是否外部上报",class:"form-control"},{default:o(()=>[a(r,{label:"所有",value:""}),a(r,{label:"是",value:"是"}),a(r,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(k,{span:6},{default:o(()=>[a(p,{label:"建设方式"},{default:o(()=>[a(_,{modelValue:t.search.construction_method,"onUpdate:modelValue":l[83]||(l[83]=e=>t.search.construction_method=e),placeholder:"请选择建设方式",class:"form-control"},{default:o(()=>[a(r,{label:"所有",value:""}),(i(!0),c(h,null,g(t.constructionMethods,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(k,{span:6},{default:o(()=>[a(p,{label:"技术路线"},{default:o(()=>[a(_,{modelValue:t.search.technical_route,"onUpdate:modelValue":l[84]||(l[84]=e=>t.search.technical_route=e),placeholder:"请选择技术路线",class:"form-control"},{default:o(()=>[a(r,{label:"所有",value:""}),(i(!0),c(h,null,g(t.technicalRoutes,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(k,{span:6},{default:o(()=>[a(p,{label:"运行状态"},{default:o(()=>[a(_,{modelValue:t.search.operation_status,"onUpdate:modelValue":l[85]||(l[85]=e=>t.search.operation_status=e),placeholder:"请选择运行状态",class:"form-control"},{default:o(()=>[a(r,{label:"所有",value:""}),a(r,{label:"建设中",value:"建设中"}),a(r,{label:"运行中",value:"运行中"}),a(r,{label:"已下线",value:"已下线"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(k,{span:6},{default:o(()=>[a(p,{label:"信创状态"},{default:o(()=>[a(_,{modelValue:t.search.xinchuang_status,"onUpdate:modelValue":l[86]||(l[86]=e=>t.search.xinchuang_status=e),placeholder:"请选择信创状态",class:"form-control"},{default:o(()=>[a(r,{label:"所有",value:""}),a(r,{label:"未信创",value:"未信创"}),a(r,{label:"完成开发或测试",value:"完成开发或测试"}),a(r,{label:"非全栈双轨",value:"非全栈双轨"}),a(r,{label:"非全栈单轨",value:"非全栈单轨"}),a(r,{label:"全栈双轨",value:"全栈双轨"}),a(r,{label:"全栈单轨",value:"全栈单轨"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(k,{span:6},{default:o(()=>[a(p,{label:"等保等级"},{default:o(()=>[a(_,{modelValue:t.search.security_level,"onUpdate:modelValue":l[87]||(l[87]=e=>t.search.security_level=e),placeholder:"请选择等保等级",class:"form-control"},{default:o(()=>[a(r,{label:"所有",value:""}),a(r,{label:"一级",value:"一级"}),a(r,{label:"二级",value:"二级"}),a(r,{label:"三级",value:"三级"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(k,{span:6},{default:o(()=>[a(p,{label:"业务线条"},{default:o(()=>[a(_,{modelValue:t.search.business_line,"onUpdate:modelValue":l[88]||(l[88]=e=>t.search.business_line=e),placeholder:"请选择业务线条",filterable:"",clearable:"",class:"form-control"},{default:o(()=>[a(r,{label:"所有",value:""}),(i(!0),c(h,null,g(t.businessLines,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(k,{span:6},{default:o(()=>[a(p,{label:"业务系统大类"},{default:o(()=>[a(_,{modelValue:t.search.system_category,"onUpdate:modelValue":l[89]||(l[89]=e=>t.search.system_category=e),placeholder:"请选择业务系统大类",filterable:"",clearable:"",class:"form-control"},{default:o(()=>[a(r,{label:"所有",value:""}),(i(!0),c(h,null,g(t.systemCategories,e=>(i(),D(r,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(k,{span:24,class:"search-buttons-col"},{default:o(()=>[a(p,{label:" ",class:"form-item-with-label search-buttons"},{default:o(()=>[s("div",Cl,[a(U,{type:"primary",onClick:d.loadData},{default:o(()=>[a(N,null,{default:o(()=>[a(J)]),_:1}),V("查询 ")]),_:1},8,["onClick"]),a(U,{onClick:d.resetSearch},{default:o(()=>[V("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),s("div",Al,[s("div",Sl,[a(U,{type:"success",disabled:!t.hasInsertPermission,onClick:d.handleAdd},{default:o(()=>[a(N,null,{default:o(()=>[a(R)]),_:1}),V("新增系统 ")]),_:1},8,["disabled","onClick"])]),s("div",jl,[a(U,{type:"info",onClick:d.exportData},{default:o(()=>[a(N,null,{default:o(()=>[a(z)]),_:1}),V(" 导出数据 ")]),_:1},8,["onClick"])])]),a(q,{class:"table-card"},{default:o(()=>[K((i(),D(I,{data:t.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:d.handleSortChange},{default:o(()=>[C("",!0),a(b,{prop:"self_build_system_id",label:"自建系统编号",sortable:""}),a(b,{prop:"system_abbreviation",label:"业务系统简称",sortable:""}),a(b,{label:"主岗",sortable:""},{default:o(e=>[V(x(d.getUserRealName(e.row.main_admin)),1)]),_:1}),a(b,{label:"备岗",sortable:""},{default:o(e=>[V(x(d.getUserRealName(e.row.backup_admin)),1)]),_:1}),a(b,{prop:"business_department",label:"业务主管部门",sortable:""}),a(b,{prop:"system_attribute",label:"系统属性",sortable:""}),a(b,{prop:"go_live_date",label:"上线时间",sortable:""}),a(b,{prop:"decommission_date",label:"下线时间",sortable:""}),a(b,{prop:"major_milestones",label:"重大历程",sortable:""}),a(b,{prop:"industry_name",label:"业务系统行业名称",sortable:""}),a(b,{prop:"monitoring_system_name",label:"业务系统英文简称",sortable:""}),a(b,{prop:"system_function_summary",label:"系统功能简述",sortable:""}),a(b,{prop:"system_form",label:"系统形态",sortable:""}),a(b,{prop:"cs_client_name",label:"CS客户端程序名称",sortable:""}),a(b,{prop:"bs_url",label:"BS URL地址",sortable:""}),a(b,{prop:"ip_port",label:"IP:端口",sortable:""}),a(b,{label:"业务线条",sortable:""},{default:o(e=>[V(x(d.getBusinessLineName(e.row.business_line)),1)]),_:1}),a(b,{label:"业务系统大类",sortable:""},{default:o(e=>[V(x(d.getSystemCategoryName(e.row.system_category)),1)]),_:1}),a(b,{prop:"system_level",label:"系统分级",sortable:""}),a(b,{prop:"has_backup_strategy",label:"是否建立备份策略",sortable:""}),a(b,{prop:"server_count",label:"对应服务器数量",sortable:""}),a(b,{prop:"monitoring_coverage",label:"监控覆盖率",sortable:""}),a(b,{prop:"digital_classification",label:"数字化分类",sortable:""}),a(b,{label:"JR/T 0059-2010备份能力标准",sortable:""},{default:o(e=>[V(x(d.getBackupStandardName(e.row.jrt_0059_backup_standard)),1)]),_:1}),a(b,{prop:"xinchuang_category_major",label:"系统分类",sortable:""}),a(b,{prop:"xinchuang_category_minor",label:"信创小类",sortable:""}),a(b,{prop:"software_copyright_name",label:"软著名称",sortable:""}),a(b,{prop:"construction_method",label:"建设方式",sortable:""}),a(b,{prop:"technical_route",label:"技术路线",sortable:""}),a(b,{prop:"is_reported_to_external",label:"是否外部上报",sortable:""}),a(b,{prop:"centos7_count",label:"CentOS7数量",sortable:""}),a(b,{prop:"operation_status",label:"运行状态",sortable:""}),a(b,{prop:"xinchuang_status",label:"信创状态",sortable:""}),a(b,{prop:"security_level",label:"等保等级",sortable:""}),a(b,{label:"通用功能域",sortable:"",width:"200"},{default:o(e=>[e.row.general_function_domains?(i(),c("div",Fl,[a(O,{placement:"right",trigger:"click",width:400,"popper-class":"domain-popover"},{reference:o(()=>[s("div",{class:"domain-preview",onClick:l[90]||(l[90]=(...w)=>d.handleDomainClick&&d.handleDomainClick(...w))},[V(x(d.formatFunctionDomainNames(e.row.general_function_domains,"generalFunctionDomains",!0))+" ",1),d.countCoreDomains(e.row.general_function_domains,"generalFunctionDomains")>0?(i(),c("span",Nl," (核心:"+x(d.countCoreDomains(e.row.general_function_domains,"generalFunctionDomains"))+") ",1)):C("",!0)])]),default:o(()=>[s("div",Yl,[Bl,s("div",ql,[(i(!0),c(h,null,g(d.parseDomainDetails(e.row.general_function_domains,"generalFunctionDomains"),(w,Y)=>(i(),c("div",{key:Y,class:P({"domain-item":!0,"core-domain":w.isCore})},[V(x(w.name)+" ",1),w.isCore?(i(),c("span",Ol,"核心")):C("",!0)],2))),128))]),s("div",Pl,[V(" 总计: "+x(d.getDomainCount(e.row.general_function_domains))+" 项 ",1),d.countCoreDomains(e.row.general_function_domains,"generalFunctionDomains")>0?(i(),c("span",Ll," (其中核心功能域: "+x(d.countCoreDomains(e.row.general_function_domains,"generalFunctionDomains"))+" 项) ",1)):C("",!0)])])]),_:2},1024)])):(i(),c("span",Jl,"-"))]),_:1}),a(b,{label:"经营机构功能域",sortable:"",width:"200"},{default:o(e=>[e.row.futures_function_domains?(i(),c("div",Ml,[a(O,{placement:"right",trigger:"click",width:400,"popper-class":"domain-popover"},{reference:o(()=>[s("div",{class:"domain-preview",onClick:l[91]||(l[91]=(...w)=>d.handleDomainClick&&d.handleDomainClick(...w))},[V(x(d.formatFunctionDomainNames(e.row.futures_function_domains,"futuresFunctionDomains",!0))+" ",1),d.countCoreDomains(e.row.futures_function_domains,"futuresFunctionDomains")>0?(i(),c("span",Rl," (核心:"+x(d.countCoreDomains(e.row.futures_function_domains,"futuresFunctionDomains"))+") ",1)):C("",!0)])]),default:o(()=>[s("div",zl,[Il,s("div",Wl,[(i(!0),c(h,null,g(d.parseDomainDetails(e.row.futures_function_domains,"futuresFunctionDomains"),(w,Y)=>(i(),c("div",{key:Y,class:P({"domain-item":!0,"core-domain":w.isCore})},[V(x(w.name)+" ",1),w.isCore?(i(),c("span",El,"核心")):C("",!0)],2))),128))]),s("div",Tl,[V(" 总计: "+x(d.getDomainCount(e.row.futures_function_domains))+" 项 ",1),d.countCoreDomains(e.row.futures_function_domains,"futuresFunctionDomains")>0?(i(),c("span",Ql," (其中核心功能域: "+x(d.countCoreDomains(e.row.futures_function_domains,"futuresFunctionDomains"))+" 项) ",1)):C("",!0)])])]),_:2},1024)])):(i(),c("span",Gl,"-"))]),_:1}),a(b,{prop:"remarks",label:"备注",sortable:""}),a(b,{prop:"created_at",label:"创建时间",sortable:""}),a(b,{prop:"created_by",label:"创建人",sortable:""}),a(b,{prop:"updated_at",label:"更新时间",sortable:""}),a(b,{prop:"updated_by",label:"更新人",sortable:""}),a(b,{label:"操作",fixed:"right"},{default:o(e=>[s("div",Hl,[a(U,{size:"small",type:"warning",disabled:!t.hasUpdatePermission,onClick:w=>d.handleEdit(e.$index,e.row)},{default:o(()=>[V("编辑")]),_:2},1032,["disabled","onClick"]),a(U,{size:"small",type:"danger",disabled:!t.hasDeletePermission,onClick:w=>d.handleDelete(e.$index,e.row)},{default:o(()=>[V("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[E,t.loading]]),s("div",Kl,[a(W,{background:"","current-page":t.search.currentPage,"page-size":t.search.pageSize,total:t.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:d.handlePageSizeChange,onCurrentChange:d.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const ea=T(ae,[["render",Xl],["__scopeId","data-v-44f3bdd7"]]);export{ea as default};
