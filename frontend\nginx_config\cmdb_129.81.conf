# 修复版本 - 解决405错误和静态资源404问题
# 定义缓存路径和参数
proxy_cache_path /var/cache/nginx/cmdb levels=1:2 keys_zone=cmdb_cache:10m max_size=1g inactive=60m;
proxy_temp_path /var/cache/nginx/temp;

# 定义浏览器缓存映射
map $sent_http_content_type $expires {
    default                    off;
    text/html                  epoch;  # 不缓存HTML
    text/css                   max;    # CSS文件长期缓存
    application/javascript     max;    # JS文件长期缓存
    ~image/                    max;    # 图片长期缓存
    ~font/                     max;    # 字体长期缓存
}

# 后端服务器组 - 负载均衡配置
upstream cmdb-web {
    server 127.0.0.1:3000 weight=1 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# 应用切换配置 - 可以切换到备用服务器
# 通过修改这个upstream来实现应用切换
# 默认指向本地，需要切换时修改为备用服务器
upstream cmdb-app-switch {
    # 默认配置：指向本地前端
    server 127.0.0.1:9000 weight=1 max_fails=3 fail_timeout=30s;

    # 切换配置：取消注释下面这行，注释上面这行，即可切换到备用服务器
    # server *************:9000 weight=1 max_fails=3 fail_timeout=30s;

    keepalive 16;
}

# CMDB主服务器 - 9000端口
server {
    listen       9000;
    server_name  *************;

    # 启用浏览器缓存
    expires $expires;

    # 添加调试头信息
    add_header X-Primary-Server "true" always;
    add_header X-Server-Port "9000" always;

    # 静态资源文件 - 优先处理，避免被SPA路由拦截
    location /assets/ {
        root /opt/cmdb_v2.0/frontend/dist;
        expires max;
        add_header Cache-Control "public, max-age=31536000, immutable";
        access_log off;

        # 如果文件不存在，尝试从根目录查找
        try_files $uri $uri/ =404;

        # 添加主服务器标识
        add_header X-Server "Primary-Server" always;
        add_header X-Resource-Type "Static-Asset" always;
    }

    # 前端静态文件 - SPA路由处理
    location / {
        root /opt/cmdb_v2.0/frontend/dist;
        try_files $uri $uri/ /index.html;
        index index.html;

        # 添加安全相关的HTTP头
        add_header X-Content-Type-Options nosniff;
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-XSS-Protection "1; mode=block";

        # 启用访问控制
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization";
    }

    # 静态资源优化
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        root /opt/cmdb_v2.0/frontend/dist;
        expires max;
        add_header Cache-Control "public, max-age=31536000, immutable";
        access_log off;  # 不记录静态资源访问日志，减少I/O
    }

    # API代理配置 - 修复405错误
    location /api/ {
        # 添加调试信息
        add_header X-API-Proxy "direct" always;

        proxy_pass http://127.0.0.1:3000/api/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Primary-Server "true";

        # 确保请求方法不被改变 - 关键修复
        proxy_method $request_method;
        proxy_pass_request_body on;
        proxy_pass_request_headers on;

        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # 确保不缓存API响应
        proxy_cache_bypass 1;
        proxy_no_cache 1;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;

        # 启用CORS
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;

        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }

    # 文件上传代理
    location /upload/ {
        proxy_pass http://127.0.0.1:3000/upload/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 文件上传特殊配置
        client_max_body_size 100M;
        proxy_request_buffering off;

        # 确保请求方法不被改变
        proxy_method $request_method;
        proxy_pass_request_body on;
        proxy_pass_request_headers on;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# 80端口服务器 - 负载均衡入口
server {
    listen              80;
    server_name         *************;

    # 启用压缩
    gzip on;
    gzip_min_length 1000;
    gzip_proxied any;

    # 启用浏览器缓存
    expires $expires;

    # 添加调试头信息
    add_header X-Backup-Server "true" always;
    add_header X-Server-Port "80" always;

    # 静态资源文件 - 优先处理，避免被SPA路由拦截
    location /assets/ {
        root /opt/cmdb_v2.0/frontend/dist;
        expires max;
        add_header Cache-Control "public, max-age=31536000, immutable";
        access_log off;

        # 如果文件不存在，尝试从根目录查找
        try_files $uri $uri/ =404;

        # 添加备用服务器标识（端口80）
        add_header X-Server "Primary-Server-Port80" always;
        add_header X-Resource-Type "Static-Asset" always;
    }

    # 所有请求转发到后端服务器 - SPA路由处理
    location / {
        root /opt/cmdb_v2.0/frontend/dist;
        try_files $uri $uri/ @proxy_to_backend;
        index index.html;
    }

    # 代理到后端的fallback
    location @proxy_to_backend {
        proxy_pass http://cmdb-web;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Backup-Server "true";

        # 确保请求方法不被改变
        proxy_method $request_method;
        proxy_pass_request_body on;
        proxy_pass_request_headers on;

        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # 负载均衡配置
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 30s;
    }

    # 专门处理API请求 - 修复405错误
    location /api/ {
        # 添加调试信息
        add_header X-API-Proxy "load-balanced" always;

        proxy_pass http://cmdb-web/api/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Backup-Server "true";

        # 确保请求方法不被改变 - 关键修复
        proxy_method $request_method;
        proxy_pass_request_body on;
        proxy_pass_request_headers on;

        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # 确保不缓存API响应
        proxy_cache_bypass 1;
        proxy_no_cache 1;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;

        # 负载均衡配置
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 30s;
    }

    # 文件上传代理
    location /upload/ {
        proxy_pass http://cmdb-web/upload/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 文件上传特殊配置
        client_max_body_size 100M;
        proxy_request_buffering off;

        # 确保请求方法不被改变
        proxy_method $request_method;
        proxy_pass_request_body on;
        proxy_pass_request_headers on;
    }

    # 健康检查端点
    location /health {
        access_log off;
        add_header Content-Type text/plain;
        return 200 "Primary Server OK - Fixed Version";
    }
}

# 应用切换服务器 - 8080端口
# 通过访问这个端口可以切换到备用服务器的应用
server {
    listen       8080;
    server_name  *************;

    # 添加切换服务器标识
    add_header X-Switch-Server "true" always;
    add_header X-Target-Server "*************:9000" always;

    # 切换管理页面
    location /switch-status {
        access_log off;
        add_header Content-Type text/html;
        return 200 '
<!DOCTYPE html>
<html>
<head>
    <title>CMDB应用切换管理</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 15px; margin: 10px 0; border-radius: 4px; }
        .primary { background: #e7f5e7; border-left: 4px solid #52c41a; }
        .backup { background: #fff7e6; border-left: 4px solid #fa8c16; }
        .info { background: #e6f7ff; border-left: 4px solid #1890ff; }
        .warning { background: #fff2e8; border-left: 4px solid #fa541c; }
        h1 { color: #333; }
        h2 { color: #666; margin-top: 30px; }
        code { background: #f6f8fa; padding: 2px 6px; border-radius: 3px; }
        .command { background: #f6f8fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 CMDB应用切换管理</h1>

        <div class="status primary">
            <strong>当前状态：</strong>主服务器模式 (*************:9000)
        </div>

        <div class="status backup">
            <strong>备用服务器：</strong>*************:9000
        </div>

        <h2>📋 切换操作说明</h2>

        <div class="info">
            <strong>方式一：通过代理切换（推荐）</strong><br>
            访问 <code>http://*************:8080/</code> 直接使用备用服务器应用
        </div>

        <div class="warning">
            <strong>方式二：永久切换配置</strong><br>
            修改nginx配置文件中的upstream配置：
            <div class="command">
# 编辑配置文件
vi /etc/nginx/conf.d/cmdb_129.81.conf

# 找到 cmdb-app-switch 部分，注释当前行，取消注释备用服务器行
# server 127.0.0.1:9000 weight=1 max_fails=3 fail_timeout=30s;
server *************:9000 weight=1 max_fails=3 fail_timeout=30s;

# 重载nginx配置
nginx -s reload
            </div>
        </div>

        <h2>🔍 服务器状态检查</h2>
        <div class="info">
            <strong>主服务器健康检查：</strong><br>
            <code>curl http://*************:9000/health</code><br><br>
            <strong>备用服务器健康检查：</strong><br>
            <code>curl http://*************:9000/health</code><br><br>
            <strong>切换服务状态：</strong><br>
            <code>curl http://*************:8080/switch-status</code>
        </div>

        <h2>⚠️ 注意事项</h2>
        <div class="warning">
            <ul>
                <li>切换前请确保备用服务器正常运行</li>
                <li>建议在维护窗口期间进行切换操作</li>
                <li>切换后请验证所有功能正常</li>
                <li>如需回切，按相同步骤操作即可</li>
            </ul>
        </div>
    </div>
</body>
</html>';
    }

    # 代理到备用服务器的应用
    location / {
        # 添加切换标识
        add_header X-Switched-To "backup-server" always;
        add_header X-Original-Server "*************" always;

        proxy_pass http://*************:9000/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Switched-From "primary-server";

        # 确保请求方法不被改变
        proxy_method $request_method;
        proxy_pass_request_body on;
        proxy_pass_request_headers on;

        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # 不缓存
        proxy_cache_bypass 1;
        proxy_no_cache 1;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;

        # 错误处理
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 30s;
    }

    # 健康检查
    location /health {
        access_log off;
        add_header Content-Type text/plain;
        return 200 "Switch Server OK - Proxying to *************:9000";
    }
}

# 全局配置
client_max_body_size 100M;
proxy_buffering off;
proxy_request_buffering off;
