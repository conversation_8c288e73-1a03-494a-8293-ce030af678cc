import{_ as we,a9 as ae,a3 as te,c as p,B as le,a as i,C as be,m as k,w as o,f as s,F as Y,l as $,x as V,t as b,b as C,h as v,g as De,r as D,K as H,o as Ce,p as ke,q as Ve,E as f,R as Me,M as q,e as d,aa as Ne,n as Se}from"./index-BnaD8Tdo.js";import{t as Pe,p as oe,f as ne,a as G}from"./dateUtils-BpS9tf2V.js";const xe={name:"OpsCalendar",components:{Refresh:te,Calendar:ae},setup(){const F=Me(),{proxy:r}=De(),B=D(!1),t=D(!1),A=D(!1),O=D(!1),L=D(!1),g=D((()=>{const e=localStorage.getItem("ops_calendar_selected_month");if(e)return console.log("从localStorage恢复月份:",e),e;{const l=new Date().toISOString().slice(0,7);return console.log("首次访问，使用当前月份:",l),l}})()),y=D([]),I=D(""),M=D([]),R=D([]),N=D({main_duty_user:"",deputy_duty_user:"",duty_manager:"",simulation_user:"",inspection_user:""}),_=D(!1),c=D(!1),S=D(!1),j=H(()=>{const[e,l]=g.value.split("-").map(Number);return`${e}年${l}月`}),W=H(()=>{if(!I.value)return"";const e=new Date(I.value);return`${e.getFullYear()}年${e.getMonth()+1}月${e.getDate()}日`}),a=H(()=>{if(!y.value||y.value.length===0)return 0;const[e,l]=g.value.split("-").map(Number),u=`${e}${l.toString().padStart(2,"0")}`;console.log("计算月度变更统计:",{currentDate:g.value,monthPrefix:u,calendarDataLength:y.value.length});let h=0;for(const m of y.value)if(m.day&&typeof m.day=="string"&&m.day.startsWith(u)){const U=Number(m.change_count)||0;h+=U,U>0&&console.log(`找到变更: ${m.day}, 变更数量: ${U} (原始值: ${m.change_count}, 类型: ${typeof m.change_count})`)}return console.log("月度变更总数:",h),h}),z=H(()=>{const[e,l]=g.value.split("-").map(Number),u=new Date(e,l-1,1),m=new Date(e,l,0).getDate(),U=u.getDay();console.log("=== 日历计算开始 ==="),console.log(`年月: ${e}-${l}`),console.log(`本月天数: ${m}`),console.log(`1号是星期: ${U} (0=周日)`);const T=[];for(let w=0;w<U;w++){const P=new Date(e,l-1,-U+w+1);T.push({date:G(P),dayNumber:P.getDate(),isCurrentMonth:!1,isWorkday:!1,isHoliday:!1,changeCount:0,changeSummary:""})}console.log(`添加了 ${U} 个上月填充天数`);for(let w=1;w<=m;w++){const P=new Date(e,l-1,w),Z=G(P),ee=ne(P),n=y.value.find(pe=>pe.day===ee);w<=3&&(console.log(`日期匹配调试 - 日期: ${w}, 前端格式: ${Z}, 数据库格式: ${ee}`),console.log("匹配到的数据:",n),n&&console.log(`工作日标记: wrk=${n.wrk}, 变更数量: ${n.change_count}`)),T.push({date:Z,dayNumber:w,isCurrentMonth:!0,isWorkday:n?n.wrk===1:P.getDay()>=1&&P.getDay()<=5,isHoliday:n?n.spr===1:!1,changeCount:n?n.change_count:0,changeSummary:n?n.change_summary:"",mainDutyUser:n?n.main_duty_user:null,deputyDutyUser:n?n.deputy_duty_user:null,dutyManager:n?n.duty_manager:null,simulationUser:n?n.simulation_user:null,inspectionUser:n?n.inspection_user:null,mainDutyName:n?n.main_duty_name:null,deputyDutyName:n?n.deputy_duty_name:null,dutyManagerName:n?n.duty_manager_name:null,simulationName:n?n.simulation_name:null,inspectionName:n?n.inspection_name:null})}console.log(`添加了 ${m} 个当月天数`);const X=42-T.length;for(let w=1;w<=X;w++){const P=new Date(e,l,w);T.push({date:G(P),dayNumber:P.getDate(),isCurrentMonth:!1,isWorkday:!1,isHoliday:!1,changeCount:0,changeSummary:""})}return console.log(`添加了 ${X} 个下月填充天数`),console.log(`总计 ${T.length} 个日历格子`),console.log("=== 日历计算完成 ==="),T});Pe();const E=async()=>{B.value=!0;try{const[e,l]=g.value.split("-").map(Number),u=await r.$axios.post("/api/get_ops_calendar",{year:e,month:l});if(u.data.code===0){y.value=u.data.msg,console.log("获取到的日历数据总数:",u.data.msg.length),console.log("日历数据示例:",u.data.msg.slice(0,3));const h=u.data.msg.filter(m=>m.change_count>0);console.log("有变更的日期:",h),h.length>0&&console.log("变更数据类型检查:",{change_count:h[0].change_count,type:typeof h[0].change_count,day:h[0].day})}else f.error(`获取日历数据失败: ${u.data.msg}`)}catch(e){console.error("获取日历数据失败:",e),f.error("获取日历数据失败")}finally{B.value=!1}},se=async e=>{t.value=!0;try{const l=oe(e);console.log(`获取变更详情，原始日期: ${e}, 转换后: ${l}`);const u=await r.$axios.post("/api/get_ops_calendar_changes",{date:l});u.data.code===0?M.value=u.data.msg:f.error(`获取变更详情失败: ${u.data.msg}`)}catch(l){console.error("获取变更详情失败:",l),f.error("获取变更详情失败")}finally{t.value=!1}},ie=e=>{const l=["calendar-day"];return e.isCurrentMonth?(e.isHoliday?l.push("holiday"):e.isWorkday?l.push("workday"):l.push("weekend"),e.changeCount>0&&l.push("has-change")):l.push("other-month"),l.join(" ")},re=async()=>{console.log("=== 月份切换开始 ==="),console.log("新选择的月份:",g.value),localStorage.setItem("ops_calendar_selected_month",g.value),y.value=[],await q(),await E(),console.log("=== 月份切换完成 ===")},de=()=>{E()},ce=async()=>{const e=new Date().toISOString().slice(0,7);console.log(`回到当前月: ${g.value} -> ${e}`),g.value!==e?(g.value=e,localStorage.setItem("ops_calendar_selected_month",e),y.value=[],await q(),await E()):f.info("当前已经是本月")},ue=async()=>{const[e,l]=g.value.split("-").map(Number);let u=e,h=l-1;h<1&&(h=12,u=e-1);const m=`${u}-${h.toString().padStart(2,"0")}`;console.log(`切换到上个月: ${g.value} -> ${m}`),g.value=m,localStorage.setItem("ops_calendar_selected_month",m),y.value=[],await q(),await E()},me=async()=>{const[e,l]=g.value.split("-").map(Number);let u=e,h=l+1;h>12&&(h=1,u=e+1);const m=`${u}-${h.toString().padStart(2,"0")}`;console.log(`切换到下个月: ${g.value} -> ${m}`),g.value=m,localStorage.setItem("ops_calendar_selected_month",m),y.value=[],await q(),await E()},ge=e=>{e.isCurrentMonth&&(I.value=e.date,console.log("点击日期，当前权限状态:",{hasEditPermission:_.value,hasViewPermission:c.value,changeCount:e.changeCount}),e.changeCount>0?(console.log(`点击日期: ${e.date}, 数据库日期: ${ne(new Date(e.date))}`),A.value=!0,se(e.date)):(console.log("无变更日期，检查权限..."),_.value===!0?(console.log("有编辑权限，显示值班排班对话框"),K(e)):(console.log("无编辑权限，给出提示"),c.value===!0?f.info("您只有查看权限，无法编辑值班排班。如需查看值班信息，请右键点击日期。"):f.warning("您没有交易日历的编辑权限"))))},_e=e=>{e.isCurrentMonth&&(console.log("右键点击日期，当前权限状态:",{hasEditPermission:_.value,hasViewPermission:c.value}),c.value===!0||_.value===!0?(console.log("有权限，显示值班排班对话框"),K(e)):(console.log("无权限，拒绝访问"),f.warning("您没有交易日历的编辑权限")))},K=e=>{I.value=e.date,N.value={main_duty_user:e.mainDutyUser||"",deputy_duty_user:e.deputyDutyUser||"",duty_manager:e.dutyManager||"",simulation_user:e.simulationUser||"",inspection_user:e.inspectionUser||""},O.value=!0},Q=async()=>{S.value=!0,_.value=!1,c.value=!1;try{const e=localStorage.getItem("username")||localStorage.getItem("loginUsername")||"admin";console.log("检查用户权限，用户名:",e);const l=await r.$axios.post("/api/check_ops_calendar_duty_permission",{username:e});console.log("权限检查API响应:",l.data),l.data.code===0?(_.value=!!l.data.msg.hasEditPermission,c.value=!!l.data.msg.hasViewPermission,console.log("用户权限检查结果:",{username:e,hasEditPermission:_.value,hasViewPermission:c.value,rawPermissions:l.data.msg}),!c.value&&!_.value?(console.log("用户无任何权限"),f.warning("您没有交易日历的编辑权限")):console.log("用户有权限:",{view:c.value,edit:_.value})):(console.error("权限检查API返回错误:",l.data.msg),f.error(`权限检查失败: ${l.data.msg}`),_.value=!1,c.value=!1)}catch(e){console.error("权限检查请求失败:",e),f.error("权限检查失败"),_.value=!1,c.value=!1}finally{S.value=!1,console.log("权限检查完成，最终权限状态:",{hasEditPermission:_.value,hasViewPermission:c.value})}},he=async()=>{try{const e=await r.$axios.post("/api/get_ops_calendar_users");e.data.code===0?R.value=e.data.msg:f.error(`获取用户列表失败: ${e.data.msg}`)}catch(e){console.error("获取用户列表失败:",e),f.error("获取用户列表失败")}},ye=async()=>{L.value=!0;try{const e=oe(I.value);console.log(`保存值班排班，原始日期: ${I.value}, 转换后: ${e}`);const l=await r.$axios.post("/api/update_ops_calendar_duty",{day:e,main_duty_user:N.value.main_duty_user||null,deputy_duty_user:N.value.deputy_duty_user||null,duty_manager:N.value.duty_manager||null,simulation_user:N.value.simulation_user||null,inspection_user:N.value.inspection_user||null,updated_by:localStorage.getItem("username")||"admin"});l.data.code===0?(f.success("值班排班保存成功"),O.value=!1,E()):f.error(`保存失败: ${l.data.msg}`)}catch(e){console.error("保存值班排班失败:",e),f.error("保存值班排班失败")}finally{L.value=!1}},fe=e=>{F.push(`/ops_change_management/detail/${e.id}`)},ve=()=>{const e=localStorage.getItem("username")||localStorage.getItem("loginUsername")||"admin";f({message:`当前用户: ${e}
编辑权限: ${_.value}
查看权限: ${c.value}`,type:"info",duration:5e3,dangerouslyUseHTMLString:!0}),console.log("=== 权限调试信息 ==="),console.log("当前用户名:",e),console.log("localStorage中的用户名:",{username:localStorage.getItem("username"),loginUsername:localStorage.getItem("loginUsername")}),console.log("权限状态:",{hasEditPermission:_.value,hasViewPermission:c.value,permissionLoading:S.value}),console.log("===================")};return Ce(async()=>{console.log("页面加载，开始检查权限..."),await Q(),console.log("权限检查完成，权限状态:",{hasEditPermission:_.value,hasViewPermission:c.value}),E(),he()}),{loading:B,dialogLoading:t,dialogVisible:A,dutyDialogVisible:O,dutyLoading:L,currentDate:g,calendarDays:z,selectedDate:I,selectedDateChanges:M,userList:R,dutyForm:N,currentMonthTitle:j,selectedDateFormatted:W,monthChangeCount:a,hasEditPermission:_,hasViewPermission:c,permissionLoading:S,Refresh:te,Calendar:ae,getDayClass:ie,handleDateChange:re,refreshCalendar:de,goToCurrentMonth:ce,goToPreviousMonth:ue,goToNextMonth:me,handleDayClick:ge,handleDayRightClick:_e,showDutyDialog:K,saveDutySchedule:ye,viewChangeDetail:fe,checkUserPermission:Q,debugPermissions:ve}}},J=F=>(ke("data-v-b65d4247"),F=F(),Ve(),F),Ie={class:"calendar-container"},Ue=J(()=>s("div",{class:"operation-description"},[s("strong",null,"左右键点击说明："),v("变更和值班同时存在时，左键查看变更详情，右键编辑值班排班 ")],-1)),Ee={class:"card-header"},Fe={class:"header-left"},Le={class:"card-title"},Te=J(()=>s("div",{class:"header-center"},[s("div",{class:"legend"},[s("span",{class:"legend-item"},[s("span",{class:"legend-color workday"}),v(" 工作日 ")]),s("span",{class:"legend-item"},[s("span",{class:"legend-color weekend"}),v(" 非工作日 ")]),s("span",{class:"legend-item"},[s("span",{class:"legend-color has-change"}),v(" 有变更 ")])])],-1)),Ye={class:"header-right"},$e={class:"month-selector"},Be={class:"calendar-grid"},Oe=J(()=>s("div",{class:"week-header"},[s("div",{class:"week-day"},"日"),s("div",{class:"week-day"},"一"),s("div",{class:"week-day"},"二"),s("div",{class:"week-day"},"三"),s("div",{class:"week-day"},"四"),s("div",{class:"week-day"},"五"),s("div",{class:"week-day"},"六")],-1)),Re={class:"calendar-body"},We=["onClick","onContextmenu"],ze={class:"day-header"},He={class:"day-number"},qe=["title"],Ae={class:"day-content"},je={key:0,class:"duty-info"},Ke=["title"],Ge=["title"],Je=["title"],Qe=["title"],Xe=["title"],Ze={class:"dialog-content"},ea={class:"dialog-header-info"},aa={class:"dialog-footer"},ta={class:"dialog-footer"};function la(F,r,B,t,A,O){const L=C("el-tag"),x=C("el-button"),g=C("el-date-picker"),y=C("el-form-item"),I=C("el-card"),M=C("el-table-column"),R=C("el-table"),N=C("el-dialog"),_=C("el-alert"),c=C("el-option"),S=C("el-select"),j=C("el-form"),W=be("loading");return d(),p("div",Ie,[Ue,le((d(),k(I,{class:"table-card",shadow:"hover"},{header:o(()=>[s("div",Ee,[s("div",Fe,[s("span",Le,b(t.currentMonthTitle),1),t.monthChangeCount>0?(d(),k(L,{key:0,type:"primary",class:"month-tag"},{default:o(()=>[v(" 本月共 "+b(t.monthChangeCount)+" 个变更 ",1)]),_:1})):V("",!0)]),Te,s("div",Ye,[i(y,{label:"选择月份：",class:"form-item-inline"},{default:o(()=>[s("div",$e,[i(x,{size:"small",onClick:t.goToPreviousMonth,disabled:t.loading,title:"上个月"},{default:o(()=>[v(" ‹ ")]),_:1},8,["onClick","disabled"]),i(g,{modelValue:t.currentDate,"onUpdate:modelValue":r[0]||(r[0]=a=>t.currentDate=a),type:"month",placeholder:"选择月份",format:"YYYY年MM月","value-format":"YYYY-MM",onChange:t.handleDateChange,style:{width:"160px",margin:"0 8px"}},null,8,["modelValue","onChange"]),i(x,{size:"small",onClick:t.goToNextMonth,disabled:t.loading,title:"下个月"},{default:o(()=>[v(" › ")]),_:1},8,["onClick","disabled"])])]),_:1}),i(x,{type:"primary",icon:t.Refresh,onClick:t.refreshCalendar,loading:t.loading},{default:o(()=>[v(" 刷新 ")]),_:1},8,["icon","onClick","loading"]),i(x,{type:"success",onClick:t.goToCurrentMonth,disabled:t.loading,title:"回到当前月"},{default:o(()=>[v(" 当前月 ")]),_:1},8,["onClick","disabled"])])])]),default:o(()=>[s("div",Be,[Oe,s("div",Re,[(d(!0),p(Y,null,$(t.calendarDays,a=>(d(),p("div",{key:a.date,class:Se(t.getDayClass(a)),onClick:z=>t.handleDayClick(a),onContextmenu:Ne(z=>t.handleDayRightClick(a),["prevent"])},[s("div",ze,[s("div",He,b(a.dayNumber),1),a.changeCount>0?(d(),p("div",{key:0,class:"change-badge-corner",title:`${a.changeCount}个变更: ${a.changeSummary}`},b(a.changeCount),9,qe)):V("",!0)]),s("div",Ae,[a.isCurrentMonth&&(a.mainDutyName||a.deputyDutyName||a.dutyManagerName||a.simulationName||a.inspectionName)?(d(),p("div",je,[a.mainDutyName?(d(),p("div",{key:0,class:"duty-item main-duty",title:`主班: ${a.mainDutyName}`}," 主班: "+b(a.mainDutyName),9,Ke)):V("",!0),a.deputyDutyName?(d(),p("div",{key:1,class:"duty-item deputy-duty",title:`副班: ${a.deputyDutyName}`}," 副班: "+b(a.deputyDutyName),9,Ge)):V("",!0),a.dutyManagerName?(d(),p("div",{key:2,class:"duty-item manager-duty",title:`值班经理: ${a.dutyManagerName}`}," 经理: "+b(a.dutyManagerName),9,Je)):V("",!0),a.simulationName?(d(),p("div",{key:3,class:"duty-item simulation-duty",title:`仿真: ${a.simulationName}`}," 仿真: "+b(a.simulationName),9,Qe)):V("",!0),a.inspectionName?(d(),p("div",{key:4,class:"duty-item inspection-duty",title:`巡检: ${a.inspectionName}`}," 巡检: "+b(a.inspectionName),9,Xe)):V("",!0)])):V("",!0)])],42,We))),128))])])]),_:1})),[[W,t.loading]]),i(N,{modelValue:t.dialogVisible,"onUpdate:modelValue":r[2]||(r[2]=a=>t.dialogVisible=a),title:`${t.selectedDateFormatted} 变更详情`,width:"85%",top:"5vh","close-on-click-modal":!1},{footer:o(()=>[s("div",aa,[i(x,{onClick:r[1]||(r[1]=a=>t.dialogVisible=!1)},{default:o(()=>[v("关闭")]),_:1})])]),default:o(()=>[s("div",Ze,[s("div",ea,[i(L,{type:"info",size:"large"},{default:o(()=>[v(" 共找到 "+b(t.selectedDateChanges.length)+" 个变更 ",1)]),_:1})]),le((d(),k(R,{data:t.selectedDateChanges,stripe:"",style:{width:"100%"},"header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"bold"}},{default:o(()=>[i(M,{prop:"change_id",label:"变更编号",width:"150","show-overflow-tooltip":""}),i(M,{prop:"title",label:"变更名称","min-width":"200","show-overflow-tooltip":""}),i(M,{prop:"system",label:"所属系统",width:"120","show-overflow-tooltip":""}),i(M,{label:"变更级别",width:"100"},{default:o(a=>[v(b(a.row.change_level_name||a.row.change_level),1)]),_:1}),i(M,{label:"变更负责人",width:"120"},{default:o(a=>[v(b(a.row.requester_name||a.row.requester),1)]),_:1}),i(M,{label:"实施人","min-width":"150","show-overflow-tooltip":""},{default:o(a=>[v(b(a.row.implementers_name||a.row.implementers),1)]),_:1}),i(M,{label:"操作",width:"100",fixed:"right"},{default:o(a=>[i(x,{type:"primary",link:"",size:"small",onClick:z=>t.viewChangeDetail(a.row)},{default:o(()=>[v(" 查看详情 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[W,t.dialogLoading]])])]),_:1},8,["modelValue","title"]),i(N,{modelValue:t.dutyDialogVisible,"onUpdate:modelValue":r[9]||(r[9]=a=>t.dutyDialogVisible=a),title:`${t.selectedDateFormatted} 值班排班`,width:"600px","close-on-click-modal":!1},{footer:o(()=>[s("div",ta,[i(x,{onClick:r[8]||(r[8]=a=>t.dutyDialogVisible=!1)},{default:o(()=>[v(b(t.hasEditPermission?"取消":"关闭"),1)]),_:1}),t.hasEditPermission?(d(),k(x,{key:0,type:"primary",onClick:t.saveDutySchedule,loading:t.dutyLoading},{default:o(()=>[v(" 保存 ")]),_:1},8,["onClick","loading"])):V("",!0)])]),default:o(()=>[t.hasViewPermission&&!t.hasEditPermission?(d(),k(_,{key:0,title:"您只有查看权限，无法修改值班排班",type:"warning",closable:!1,style:{"margin-bottom":"16px"}})):V("",!0),!t.hasViewPermission&&!t.hasEditPermission?(d(),k(_,{key:1,title:"您没有交易日历的编辑权限",type:"error",closable:!1,style:{"margin-bottom":"16px"}})):V("",!0),i(j,{model:t.dutyForm,"label-width":"100px",class:"duty-form"},{default:o(()=>[i(y,{label:"主班人员："},{default:o(()=>[i(S,{modelValue:t.dutyForm.main_duty_user,"onUpdate:modelValue":r[3]||(r[3]=a=>t.dutyForm.main_duty_user=a),placeholder:"请选择主班人员",clearable:"",filterable:"",style:{width:"100%"},disabled:!t.hasEditPermission},{default:o(()=>[(d(!0),p(Y,null,$(t.userList,a=>(d(),k(c,{key:a.username,label:a.real_name,value:a.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),i(y,{label:"副班人员："},{default:o(()=>[i(S,{modelValue:t.dutyForm.deputy_duty_user,"onUpdate:modelValue":r[4]||(r[4]=a=>t.dutyForm.deputy_duty_user=a),placeholder:"请选择副班人员",clearable:"",filterable:"",style:{width:"100%"},disabled:!t.hasEditPermission},{default:o(()=>[(d(!0),p(Y,null,$(t.userList,a=>(d(),k(c,{key:a.username,label:a.real_name,value:a.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),i(y,{label:"值班经理："},{default:o(()=>[i(S,{modelValue:t.dutyForm.duty_manager,"onUpdate:modelValue":r[5]||(r[5]=a=>t.dutyForm.duty_manager=a),placeholder:"请选择值班经理",clearable:"",filterable:"",style:{width:"100%"},disabled:!t.hasEditPermission},{default:o(()=>[(d(!0),p(Y,null,$(t.userList,a=>(d(),k(c,{key:a.username,label:a.real_name,value:a.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),i(y,{label:"仿真人员："},{default:o(()=>[i(S,{modelValue:t.dutyForm.simulation_user,"onUpdate:modelValue":r[6]||(r[6]=a=>t.dutyForm.simulation_user=a),placeholder:"请选择仿真人员",clearable:"",filterable:"",style:{width:"100%"},disabled:!t.hasEditPermission},{default:o(()=>[(d(!0),p(Y,null,$(t.userList,a=>(d(),k(c,{key:a.username,label:a.real_name,value:a.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),i(y,{label:"巡检人员："},{default:o(()=>[i(S,{modelValue:t.dutyForm.inspection_user,"onUpdate:modelValue":r[7]||(r[7]=a=>t.dutyForm.inspection_user=a),placeholder:"请选择巡检人员",clearable:"",filterable:"",style:{width:"100%"},disabled:!t.hasEditPermission},{default:o(()=>[(d(!0),p(Y,null,$(t.userList,a=>(d(),k(c,{key:a.username,label:a.real_name,value:a.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}const ia=we(xe,[["render",la],["__scopeId","data-v-b65d4247"]]);export{ia as default};
