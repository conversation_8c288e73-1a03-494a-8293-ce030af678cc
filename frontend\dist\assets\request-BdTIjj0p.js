import{a8 as a,E as r,O as s}from"./index-BnaD8Tdo.js";const n="authToken",c="loginUsername";function i(){return localStorage.getItem(n)}function m(){return localStorage.getItem(c)}const o=a.create({baseURL:"",timeout:3e4,withCredentials:!0});o.interceptors.request.use(t=>{const e=i();return e&&(t.headers.Authorization=`Bearer ${e}`),t},t=>(console.error("请求错误:",t),Promise.reject(t)));o.interceptors.response.use(t=>{const e=t.data;return t.headers["content-type"]&&t.headers["content-type"].indexOf("application/json")===-1?e:e.code!==0&&e.code!==void 0?(r({message:e.msg||"请求失败",type:"error",duration:5*1e3}),e.code===401&&s.confirm("登录状态已过期，请重新登录","确认退出",{confirmButtonText:"重新登录",cancelButtonText:"取消",type:"warning"}).then(()=>{localStorage.removeItem("authToken"),localStorage.removeItem("tokenTimestamp"),localStorage.removeItem("pagePermissions"),localStorage.removeItem("loginUsername"),location.href="/"}),Promise.reject(new Error(e.msg||"请求失败"))):e},t=>{console.error("响应错误:",t);let e="网络错误，请检查您的网络连接";if(t.response)switch(t.response.status){case 400:e="请求错误";break;case 401:e="未授权，请重新登录",localStorage.removeItem("authToken"),localStorage.removeItem("tokenTimestamp"),localStorage.removeItem("pagePermissions"),localStorage.removeItem("loginUsername"),location.href="/";break;case 403:e="拒绝访问";break;case 404:e="请求的资源不存在";break;case 500:e="服务器内部错误";break;default:e=`请求失败: ${t.response.status}`}else t.request&&(e="服务器无响应，请稍后重试");return r({message:e,type:"error",duration:5*1e3}),Promise.reject(t)});const u=Object.freeze(Object.defineProperty({__proto__:null,default:o},Symbol.toStringTag,{value:"Module"}));export{m as a,i as g,u as r,o as s};
