import{_ as y,c as p,f as n,a as l,w as h,h as g,b as T,t as f,F as L,l as x,p as I,q as S,e as m,n as P}from"./index-BnaD8Tdo.js";const A={name:"DebugApiTest",data(){return{testResult:"",logs:[]}},methods:{addLog(s,o="info"){this.logs.unshift({timestamp:new Date().toLocaleTimeString(),message:s,type:o}),this.logs.length>50&&(this.logs=this.logs.slice(0,50))},async testDataDictionary(){var s,o,i,t,a,r,d,c,u;this.addLog("开始测试数据字典API...","info");try{this.addLog("发送POST请求到 /api/get_cmdb_data_dictionary","info"),this.addLog('请求参数: {"dict_code": "B"}',"info");const e=await this.$axios.post("/api/get_cmdb_data_dictionary",{dict_code:"B"});this.testResult=JSON.stringify({success:!0,status:e.status,statusText:e.statusText,headers:e.headers,data:e.data},null,2),this.addLog("数据字典API测试成功","success")}catch(e){this.testResult=JSON.stringify({success:!1,error:e.message,status:(s=e.response)==null?void 0:s.status,statusText:(o=e.response)==null?void 0:o.statusText,headers:(i=e.response)==null?void 0:i.headers,data:(t=e.response)==null?void 0:t.data,config:{method:(a=e.config)==null?void 0:a.method,url:(r=e.config)==null?void 0:r.url,baseURL:(d=e.config)==null?void 0:d.baseURL}},null,2),this.addLog(`数据字典API测试失败: ${e.message}`,"error"),this.addLog(`状态码: ${(c=e.response)==null?void 0:c.status}`,"error"),this.addLog(`状态文本: ${(u=e.response)==null?void 0:u.statusText}`,"error")}},async testDataDictionaryGET(){var s,o,i,t;this.addLog("开始测试数据字典API (GET方法)...","info");try{const a=await this.$axios.get("/api/get_cmdb_data_dictionary?dict_code=B");this.testResult=JSON.stringify({success:!0,method:"GET",status:a.status,data:a.data},null,2),this.addLog("GET方法测试成功（意外）","warning")}catch(a){this.testResult=JSON.stringify({success:!1,method:"GET",error:a.message,status:(s=a.response)==null?void 0:s.status,statusText:(o=a.response)==null?void 0:o.statusText,data:(i=a.response)==null?void 0:i.data},null,2),this.addLog(`GET方法测试失败（预期）: ${a.message}`,"info"),this.addLog(`状态码: ${(t=a.response)==null?void 0:t.status}`,"info")}},async testMethodEndpoint(){var s,o,i;this.addLog("开始测试方法检测API...","info");try{const t=await this.$axios.post("/api/test_method",{test:"data",timestamp:new Date().toISOString()});this.testResult=JSON.stringify(t.data,null,2),this.addLog("方法检测API测试成功","success"),this.addLog(`检测到的nginx信息: ${JSON.stringify(t.data.nginx_info)}`,"info")}catch(t){this.testResult=JSON.stringify({error:t.message,status:(s=t.response)==null?void 0:s.status,statusText:(o=t.response)==null?void 0:o.statusText,data:(i=t.response)==null?void 0:i.data},null,2),this.addLog(`方法检测API测试失败: ${t.message}`,"error")}},async testWithDifferentHeaders(){var s,o,i;this.addLog("开始测试不同请求头的数据字典API...","info");try{const t=await this.$axios.post("/api/get_cmdb_data_dictionary",{dict_code:"B"},{headers:{"X-Test-Header":"debug-test","Cache-Control":"no-cache",Pragma:"no-cache"}});this.testResult=JSON.stringify({success:!0,status:t.status,data:t.data,test_type:"with_custom_headers"},null,2),this.addLog("自定义请求头测试成功","success")}catch(t){this.testResult=JSON.stringify({success:!1,error:t.message,status:(s=t.response)==null?void 0:s.status,statusText:(o=t.response)==null?void 0:o.statusText,data:(i=t.response)==null?void 0:i.data,test_type:"with_custom_headers"},null,2),this.addLog(`自定义请求头测试失败: ${t.message}`,"error")}}}},_=s=>(I("data-v-4069a594"),s=s(),S(),s),D={class:"debug-container"},b=_(()=>n("h2",null,"API调试测试页面",-1)),w={class:"test-section"},C=_(()=>n("h3",null,"测试数据字典API",-1)),N={class:"result-section"},O=_(()=>n("h3",null,"测试结果",-1)),R={class:"log-section"},k=_(()=>n("h3",null,"控制台日志",-1)),E={class:"log-content"};function $(s,o,i,t,a,r){const d=T("el-button");return m(),p("div",D,[b,n("div",w,[C,l(d,{onClick:r.testDataDictionary,type:"primary"},{default:h(()=>[g("测试数据字典API (POST)")]),_:1},8,["onClick"]),l(d,{onClick:r.testDataDictionaryGET,type:"warning"},{default:h(()=>[g("测试数据字典API (GET)")]),_:1},8,["onClick"]),l(d,{onClick:r.testMethodEndpoint,type:"success"},{default:h(()=>[g("测试方法检测API")]),_:1},8,["onClick"]),l(d,{onClick:r.testWithDifferentHeaders,type:"info"},{default:h(()=>[g("测试不同请求头")]),_:1},8,["onClick"])]),n("div",N,[O,n("pre",null,f(a.testResult),1)]),n("div",R,[k,n("div",E,[(m(!0),p(L,null,x(a.logs,(c,u)=>(m(),p("div",{key:u,class:P(c.type)},f(c.timestamp)+" - "+f(c.message),3))),128))])])])}const J=y(A,[["render",$],["__scopeId","data-v-4069a594"]]);export{J as default};
