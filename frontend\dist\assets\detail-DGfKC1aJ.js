const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./request-BdTIjj0p.js","./index-BnaD8Tdo.js","./index-BZoqYgHj.css"])))=>i.map(i=>d[i]);
import{_ as K,a3 as te,U as oe,a4 as le,z as ne,a5 as se,J as G,r as F,o as X,c as U,f as g,a,w as t,b,h as C,F as P,l as M,t as N,m as k,x as T,E as m,a6 as re,O as ce,p as Z,q as $,e as d,Q as ie,a7 as de,R as _e}from"./index-BnaD8Tdo.js";import{g as H,s as Y}from"./request-BdTIjj0p.js";import{s as ge}from"./dateUtils-BpS9tf2V.js";const he={name:"FileAttachments",components:{Document:se,Download:ne,Upload:le,Delete:oe,Refresh:te},props:{changeData:{type:Object,required:!0},refreshChangeData:{type:Function,default:null}},emits:["update:changeData"],setup(n,{emit:r}){const p="/api/upload_ops_change_file_simple",e={Authorization:`Bearer ${H()}`},u=G({oa_process:!1,signed_archive:!1,operation_sheet:!1,supplementary_material:!1}),j=G({oa_process:!1,signed_archive:!1,operation_sheet:!1,supplementary_material:!1}),R=G({oa_process:!1,signed_archive:!1,operation_sheet:!1,supplementary_material:!1}),J=F(!1),_=F(""),y=F([]),I=F(!1),V=o=>({changeId:n.changeData.change_id,fileType:o,usernameby:localStorage.getItem("username")||"admin"}),z=o=>o?o.split("/").pop():"",L=o=>{if(!["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","image/jpeg","image/png"].includes(o.type))return m.error("文件类型不支持，请上传PDF、Word、Excel或图片文件"),!1;const s=20*1024*1024;return o.size>s?(m.error("文件大小不能超过20MB"),!1):!0},x=async(o=!1,i=!1)=>{try{if(console.log(`开始${o?"重试":""}刷新变更详情数据...${i?"(强制更新模式)":""}`),console.log("当前变更ID:",n.changeData.id),!n.changeData.id)return console.log("变更ID为空，无法刷新数据"),!1;console.log("发送请求获取最新变更数据...");const s=new Date().getTime(),v={id:n.changeData.id,_t:s};console.log("请求参数:",v);const{default:D}=await re(async()=>{const{default:h}=await import("./request-BdTIjj0p.js").then(w=>w.r);return{default:h}},__vite__mapDeps([0,1,2]),import.meta.url),f=await D({url:"/api/get_ops_change_management",method:"post",data:v,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}});if(console.log("获取变更数据响应:",JSON.stringify(f,null,2)),f.code===0&&f.msg&&f.msg.length>0){const h=f.msg[0];console.log("获取到的最新数据:",JSON.stringify(h,null,2)),console.log("附件相关字段:","附件1(OA流程):",h.oa_process,h.oa_process_file,"附件2(签字存档):",h.signed_archive,h.signed_archive_file,"附件3(操作表):",h.operation_sheet,"附件4(补充资料):",h.supplementary_material);const w={...n.changeData};w.oa_process=h.oa_process,w.oa_process_file=h.oa_process_file,w.signed_archive=h.signed_archive,w.signed_archive_file=h.signed_archive_file,w.operation_sheet=h.operation_sheet,w.supplementary_material=h.supplementary_material,console.log("更新前的数据:",{oa_process:n.changeData.oa_process,oa_process_file:n.changeData.oa_process_file,signed_archive:n.changeData.signed_archive,signed_archive_file:n.changeData.signed_archive_file,operation_sheet:n.changeData.operation_sheet,supplementary_material:n.changeData.supplementary_material}),console.log("更新后的数据:",{oa_process:w.oa_process,oa_process_file:w.oa_process_file,signed_archive:w.signed_archive,signed_archive_file:w.signed_archive_file,operation_sheet:w.operation_sheet,supplementary_material:w.supplementary_material});const W=n.changeData.oa_process!==w.oa_process||n.changeData.oa_process_file!==w.oa_process_file||n.changeData.signed_archive!==w.signed_archive||n.changeData.signed_archive_file!==w.signed_archive_file||n.changeData.operation_sheet!==w.operation_sheet||n.changeData.supplementary_material!==w.supplementary_material;if(console.log("数据是否有变化:",W),console.log("是否强制更新:",i),W||i){console.log(W?"数据有变化，更新组件数据":"强制更新模式，更新组件数据");const ae=JSON.parse(JSON.stringify(w));return r("update:changeData",ae),o||m.success(W?"附件状态已更新":"刷新成功"),!0}else return console.log("数据没有变化，无需更新"),!1}else return console.log("获取变更数据失败或数据为空"),!1}catch(s){return console.error("刷新变更详情数据失败:",s),!1}},q=async(o,i)=>{try{if(console.log("上传成功处理函数被调用"),console.log("上传响应:",o),o.code===0){m.success("文件上传成功");const s={...n.changeData};i==="oa_process"?(s.oa_process=!0,s.oa_process_file=o.msg.path):i==="signed_archive"?(s.signed_archive=!0,s.signed_archive_file=o.msg.path):i==="operation_sheet"?s.operation_sheet=o.msg.path:i==="supplementary_material"&&(s.supplementary_material=o.msg.path),console.log("更新前的数据:",n.changeData),console.log("更新后的数据:",s),r("update:changeData",s),console.log("组件数据已更新，准备刷新变更详情数据..."),m.info("正在更新附件状态，请稍候..."),setTimeout(async()=>{console.log("开始延迟刷新...");try{if(n.refreshChangeData&&typeof n.refreshChangeData=="function")console.log("使用父组件的刷新方法"),await n.refreshChangeData()?console.log("父组件刷新成功"):(console.log("父组件刷新失败或无变化，尝试第二次刷新"),setTimeout(async()=>{console.log("开始第二次刷新...");try{const D=await n.refreshChangeData();console.log("第二次刷新完成，结果:",D),D||(console.log("两次刷新均未获取到更新的数据，可能需要手动刷新"),m.info("附件状态可能需要手动刷新查看"))}catch(D){console.error("第二次刷新出错:",D)}},2e3));else{console.log("使用组件内部的刷新方法");const v=await x(!1,!1);console.log("第一次刷新完成，结果:",v),v||setTimeout(async()=>{console.log("开始第二次刷新...");try{const D=await x(!0,!1);console.log("第二次刷新完成，结果:",D),D||setTimeout(async()=>{console.log("开始第三次刷新(强制更新模式)...");try{const f=await x(!0,!0);console.log("第三次刷新完成，结果:",f),f||(console.log("三次刷新均未获取到更新的数据，可能需要手动刷新页面"),m.info("附件状态可能需要手动刷新查看"),setTimeout(()=>{const h=JSON.parse(JSON.stringify(n.changeData));r("update:changeData",h)},100))}catch(f){console.error("第三次刷新出错:",f)}},3e3)}catch(D){console.error("第二次刷新出错:",D)}},2e3)}}catch(v){console.error("第一次刷新出错:",v)}},3e3)}else m.error(`上传失败: ${o.msg}`)}catch(s){console.error("上传成功处理函数出错:",s)}finally{u[i]=!1}},O=async(o,i)=>{try{if(u[i]=!0,console.log("开始上传文件:",i),console.log("文件信息:",o.file),console.log("变更ID:",n.changeData.change_id),!n.changeData.change_id)throw new Error("变更ID不能为空，请先保存变更信息");const s=new FormData;s.append("file",o.file);const v=localStorage.getItem("username")||"admin",D=new URLSearchParams({changeId:n.changeData.change_id,fileType:i,usernameby:v}).toString();console.log("表单数据:",{changeId:n.changeData.change_id,fileType:i,usernameby:v});const f=await fetch(`${p}?${D}`,{method:"POST",headers:{Authorization:e.Authorization},body:s}),h=await f.json();if(f.ok)q(h,i);else throw new Error(h.msg||"上传失败")}catch(s){console.error("上传错误:",s),m.error(`上传失败: ${s.message}`),u[i]=!1}},E=o=>{console.error("上传错误:",o),m.error("文件上传失败，请稍后重试"),Object.keys(u).forEach(i=>{u[i]=!1})},c=async o=>{try{j[o]=!0;const i=`/api/download_ops_change_file?changeId=${n.changeData.change_id}&fileType=${o}&direct=true`,s=document.createElement("a");s.href=i,s.target="_blank",document.body.appendChild(s),s.click(),document.body.removeChild(s),m.success("文件下载成功")}catch(i){console.error("下载错误:",i),m.error(`文件下载失败: ${i.message}`)}finally{j[o]=!1}},B=o=>{ce.confirm("确定要删除此文件吗？此操作不可恢复。","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{R[o]=!0;const i=await fetch("/api/remove_ops_change_file",{method:"POST",headers:{...e,"Content-Type":"application/json"},body:JSON.stringify({changeId:n.changeData.change_id,fileType:o,usernameby:localStorage.getItem("username")||"admin"})});if(!i.ok){const v=await i.json();throw new Error(v.msg||"删除失败")}const s=await i.json();if(s.code===0){m.success("文件删除成功"),console.log("文件删除成功，准备更新组件数据");const v={...n.changeData};o==="oa_process"?(v.oa_process=!1,v.oa_process_file=null):o==="signed_archive"?(v.signed_archive=!1,v.signed_archive_file=null):o==="operation_sheet"?v.operation_sheet=null:o==="supplementary_material"&&(v.supplementary_material=null),console.log("更新前的数据:",n.changeData),console.log("更新后的数据:",v),r("update:changeData",v),console.log("组件数据已更新，准备刷新变更详情数据..."),m.info("正在更新附件状态，请稍候..."),setTimeout(async()=>{console.log("开始延迟刷新...");try{if(n.refreshChangeData&&typeof n.refreshChangeData=="function")console.log("使用父组件的刷新方法"),await n.refreshChangeData()?console.log("父组件刷新成功"):(console.log("父组件刷新失败或无变化，尝试第二次刷新"),setTimeout(async()=>{console.log("开始第二次刷新...");try{const f=await n.refreshChangeData();console.log("第二次刷新完成，结果:",f),f||(console.log("两次刷新均未获取到更新的数据，可能需要手动刷新"),m.info("附件状态可能需要手动刷新查看"))}catch(f){console.error("第二次刷新出错:",f)}},2e3));else{console.log("使用组件内部的刷新方法");const D=await x(!1,!1);console.log("第一次刷新完成，结果:",D),D||setTimeout(async()=>{console.log("开始第二次刷新...");try{const f=await x(!0,!1);console.log("第二次刷新完成，结果:",f),f||setTimeout(async()=>{console.log("开始第三次刷新(强制更新模式)...");try{const h=await x(!0,!0);console.log("第三次刷新完成，结果:",h),h||(console.log("三次刷新均未获取到更新的数据，可能需要手动刷新页面"),m.info("附件状态可能需要手动刷新查看"),setTimeout(()=>{const w=JSON.parse(JSON.stringify(n.changeData));r("update:changeData",w)},100))}catch(h){console.error("第三次刷新出错:",h)}},3e3)}catch(f){console.error("第二次刷新出错:",f)}},2e3)}}catch(D){console.error("第一次刷新出错:",D)}},3e3)}else throw new Error(s.msg||"删除失败")}catch(i){console.error("删除错误:",i),m.error(`文件删除失败: ${i.message}`)}finally{R[o]=!1}}).catch(()=>{})},Q=async()=>{try{J.value=!0,m.info("正在刷新附件状态..."),n.refreshChangeData&&typeof n.refreshChangeData=="function"?(console.log("使用父组件的刷新方法"),await n.refreshChangeData()?console.log("父组件刷新成功"):(console.log("父组件刷新失败或无变化"),m.info("刷新完成，未检测到附件状态变化"))):(console.log("使用组件内部的刷新方法"),await x(!1,!0)||m.info("刷新完成，未检测到附件状态变化"),setTimeout(()=>{const i=JSON.parse(JSON.stringify(n.changeData));r("update:changeData",i)},100))}catch(o){console.error("手动刷新出错:",o),m.error("刷新失败，请稍后重试")}finally{J.value=!1}},l=async()=>{try{const o=await Y({url:"/api/get_ops_change_templates",method:"post",data:{currentPage:1,pageSize:100}});o.code===0?(y.value=o.msg||[],console.log("获取模板列表成功:",y.value)):console.error("获取模板列表失败:",o.msg)}catch(o){console.error("获取模板列表失败:",o)}},S=async()=>{if(!_.value){m.warning("请先选择要下载的模板");return}try{I.value=!0,console.log("开始下载模板，ID:",_.value);const o=y.value.find(D=>D.id===_.value);if(!o){m.error("未找到选中的模板");return}const i=`/api/download_ops_change_template?id=${_.value}&direct=true`,s=document.createElement("a");s.href=i,s.target="_blank",s.download=o.original_filename||o.template_name,H()?window.open(i,"_blank"):(document.body.appendChild(s),s.click(),document.body.removeChild(s)),m.success(`模板 "${o.template_name}" 下载成功`),_.value=""}catch(o){console.error("下载模板失败:",o),m.error("模板下载失败，请稍后重试")}finally{I.value=!1}};return X(()=>{l()}),{uploadUrl:p,headers:e,uploadLoading:u,downloadLoading:j,removeLoading:R,refreshLoading:J,selectedTemplate:_,templateOptions:y,templateDownloadLoading:I,getUploadData:V,getFileName:z,beforeUpload:L,handleUploadSuccess:q,handleUploadError:E,customUpload:O,downloadFile:c,removeFile:B,refreshChangeData:x,handleManualRefresh:Q,getTemplateList:l,downloadTemplate:S}}},A=n=>(Z("data-v-d4460001"),n=n(),$(),n),me={class:"file-attachments-container"},ue={class:"attachments-header"},fe=A(()=>g("span",{class:"divider-title"},"附件管理",-1)),pe={class:"template-download-section"},ye={class:"template-content"},ve={class:"template-selector"},De=A(()=>g("span",{class:"template-title"},"变更操作表模板下载",-1)),we={class:"card-header"},be=A(()=>g("span",null,"OA流程",-1)),Ce={class:"card-content"},ke={key:0,class:"file-info"},xe={class:"file-name"},Ue={key:1,class:"file-placeholder"},ze=A(()=>g("span",null,"请上传OA流程文件",-1)),Oe={class:"action-buttons"},Le={class:"card-header"},Se=A(()=>g("span",null,"签字存档",-1)),Ie={class:"card-content"},Re={key:0,class:"file-info"},Ve={class:"file-name"},qe={key:1,class:"file-placeholder"},Ee=A(()=>g("span",null,"请上传签字存档文件",-1)),Ne={class:"action-buttons"},Fe={class:"card-header"},Te=A(()=>g("span",null,"变更操作表",-1)),Ae={class:"card-content"},Je={key:0,class:"file-info"},je={class:"file-name"},Be={key:1,class:"file-placeholder"},Pe=A(()=>g("span",null,"请上传变更操作表文件",-1)),Me={class:"action-buttons"},Ye={class:"card-header"},Qe=A(()=>g("span",null,"补充资料",-1)),We={class:"card-content"},Ge={key:0,class:"file-info"},He={class:"file-name"},Ke={key:1,class:"file-placeholder"},Xe=A(()=>g("span",null,"请上传补充资料文件",-1)),Ze={class:"action-buttons"};function $e(n,r,p,e,u,j){const R=b("el-divider"),J=b("Refresh"),_=b("el-icon"),y=b("el-button"),I=b("el-option"),V=b("el-select"),z=b("Download"),L=b("el-card"),x=b("el-tag"),q=b("Document"),O=b("Upload"),E=b("el-upload"),c=b("Delete"),B=b("el-col"),Q=b("el-row");return d(),U("div",me,[g("div",ue,[a(R,{"content-position":"left"},{default:t(()=>[fe]),_:1}),a(y,{type:"primary",size:"small",class:"refresh-button",loading:e.refreshLoading,onClick:e.handleManualRefresh},{default:t(()=>[a(_,null,{default:t(()=>[a(J)]),_:1}),C(" 刷新附件状态 ")]),_:1},8,["loading","onClick"])]),g("div",pe,[a(L,{shadow:"hover",class:"template-card"},{default:t(()=>[g("div",ye,[g("div",ve,[De,a(V,{modelValue:e.selectedTemplate,"onUpdate:modelValue":r[0]||(r[0]=l=>e.selectedTemplate=l),placeholder:"请选择变更操作表模板",style:{width:"50%"},size:"small",filterable:"",clearable:""},{default:t(()=>[(d(!0),U(P,null,M(e.templateOptions,l=>(d(),k(I,{key:l.id,label:l.template_name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),a(y,{type:"primary",size:"small",onClick:e.downloadTemplate,disabled:!e.selectedTemplate,loading:e.templateDownloadLoading,style:{"margin-left":"10px"}},{default:t(()=>[a(_,null,{default:t(()=>[a(z)]),_:1}),C(" 下载模板 ")]),_:1},8,["onClick","disabled","loading"])])])]),_:1})]),a(Q,{gutter:20},{default:t(()=>[a(B,{xs:24,sm:12,md:6,lg:6,xl:6},{default:t(()=>[a(L,{shadow:"hover",class:"attachment-card"},{header:t(()=>[g("div",we,[be,p.changeData.oa_process?(d(),k(x,{key:0,type:"success",size:"small"},{default:t(()=>[C("已上传")]),_:1})):(d(),k(x,{key:1,type:"info",size:"small"},{default:t(()=>[C("未上传")]),_:1}))])]),default:t(()=>[g("div",Ce,[p.changeData.oa_process?(d(),U("div",ke,[a(_,null,{default:t(()=>[a(q)]),_:1}),g("span",xe,N(e.getFileName(p.changeData.oa_process_file)),1)])):(d(),U("div",Ue,[a(_,null,{default:t(()=>[a(O)]),_:1}),ze])),g("div",Oe,[p.changeData.oa_process?(d(),k(y,{key:0,type:"primary",size:"small",onClick:r[1]||(r[1]=l=>e.downloadFile("oa_process")),loading:e.downloadLoading.oa_process},{default:t(()=>[a(_,null,{default:t(()=>[a(z)]),_:1}),C(" 下载 ")]),_:1},8,["loading"])):T("",!0),a(E,{class:"upload-button",action:e.uploadUrl,headers:e.headers,data:e.getUploadData("oa_process"),"show-file-list":!1,"before-upload":e.beforeUpload,"http-request":l=>e.customUpload(l,"oa_process")},{default:t(()=>[a(y,{type:"success",size:"small",loading:e.uploadLoading.oa_process},{default:t(()=>[a(_,null,{default:t(()=>[a(O)]),_:1}),C(" "+N(p.changeData.oa_process?"重新上传":"上传"),1)]),_:1},8,["loading"])]),_:1},8,["action","headers","data","before-upload","http-request"]),p.changeData.oa_process?(d(),k(y,{key:1,type:"danger",size:"small",onClick:r[2]||(r[2]=l=>e.removeFile("oa_process")),loading:e.removeLoading.oa_process},{default:t(()=>[a(_,null,{default:t(()=>[a(c)]),_:1}),C(" 删除 ")]),_:1},8,["loading"])):T("",!0)])])]),_:1})]),_:1}),a(B,{xs:24,sm:12,md:6,lg:6,xl:6},{default:t(()=>[a(L,{shadow:"hover",class:"attachment-card"},{header:t(()=>[g("div",Le,[Se,p.changeData.signed_archive?(d(),k(x,{key:0,type:"success",size:"small"},{default:t(()=>[C("已上传")]),_:1})):(d(),k(x,{key:1,type:"info",size:"small"},{default:t(()=>[C("未上传")]),_:1}))])]),default:t(()=>[g("div",Ie,[p.changeData.signed_archive?(d(),U("div",Re,[a(_,null,{default:t(()=>[a(q)]),_:1}),g("span",Ve,N(e.getFileName(p.changeData.signed_archive_file)),1)])):(d(),U("div",qe,[a(_,null,{default:t(()=>[a(O)]),_:1}),Ee])),g("div",Ne,[p.changeData.signed_archive?(d(),k(y,{key:0,type:"primary",size:"small",onClick:r[3]||(r[3]=l=>e.downloadFile("signed_archive")),loading:e.downloadLoading.signed_archive},{default:t(()=>[a(_,null,{default:t(()=>[a(z)]),_:1}),C(" 下载 ")]),_:1},8,["loading"])):T("",!0),a(E,{class:"upload-button",action:e.uploadUrl,headers:e.headers,data:e.getUploadData("signed_archive"),"show-file-list":!1,"before-upload":e.beforeUpload,"http-request":l=>e.customUpload(l,"signed_archive")},{default:t(()=>[a(y,{type:"success",size:"small",loading:e.uploadLoading.signed_archive},{default:t(()=>[a(_,null,{default:t(()=>[a(O)]),_:1}),C(" "+N(p.changeData.signed_archive?"重新上传":"上传"),1)]),_:1},8,["loading"])]),_:1},8,["action","headers","data","before-upload","http-request"]),p.changeData.signed_archive?(d(),k(y,{key:1,type:"danger",size:"small",onClick:r[4]||(r[4]=l=>e.removeFile("signed_archive")),loading:e.removeLoading.signed_archive},{default:t(()=>[a(_,null,{default:t(()=>[a(c)]),_:1}),C(" 删除 ")]),_:1},8,["loading"])):T("",!0)])])]),_:1})]),_:1}),a(B,{xs:24,sm:12,md:6,lg:6,xl:6},{default:t(()=>[a(L,{shadow:"hover",class:"attachment-card"},{header:t(()=>[g("div",Fe,[Te,p.changeData.operation_sheet?(d(),k(x,{key:0,type:"success",size:"small"},{default:t(()=>[C("已上传")]),_:1})):(d(),k(x,{key:1,type:"info",size:"small"},{default:t(()=>[C("未上传")]),_:1}))])]),default:t(()=>[g("div",Ae,[p.changeData.operation_sheet?(d(),U("div",Je,[a(_,null,{default:t(()=>[a(q)]),_:1}),g("span",je,N(e.getFileName(p.changeData.operation_sheet)),1)])):(d(),U("div",Be,[a(_,null,{default:t(()=>[a(O)]),_:1}),Pe])),g("div",Me,[p.changeData.operation_sheet?(d(),k(y,{key:0,type:"primary",size:"small",onClick:r[5]||(r[5]=l=>e.downloadFile("operation_sheet")),loading:e.downloadLoading.operation_sheet},{default:t(()=>[a(_,null,{default:t(()=>[a(z)]),_:1}),C(" 下载 ")]),_:1},8,["loading"])):T("",!0),a(E,{class:"upload-button",action:e.uploadUrl,headers:e.headers,data:e.getUploadData("operation_sheet"),"show-file-list":!1,"before-upload":e.beforeUpload,"http-request":l=>e.customUpload(l,"operation_sheet")},{default:t(()=>[a(y,{type:"success",size:"small",loading:e.uploadLoading.operation_sheet},{default:t(()=>[a(_,null,{default:t(()=>[a(O)]),_:1}),C(" "+N(p.changeData.operation_sheet?"重新上传":"上传"),1)]),_:1},8,["loading"])]),_:1},8,["action","headers","data","before-upload","http-request"]),p.changeData.operation_sheet?(d(),k(y,{key:1,type:"danger",size:"small",onClick:r[6]||(r[6]=l=>e.removeFile("operation_sheet")),loading:e.removeLoading.operation_sheet},{default:t(()=>[a(_,null,{default:t(()=>[a(c)]),_:1}),C(" 删除 ")]),_:1},8,["loading"])):T("",!0)])])]),_:1})]),_:1}),a(B,{xs:24,sm:12,md:6,lg:6,xl:6},{default:t(()=>[a(L,{shadow:"hover",class:"attachment-card"},{header:t(()=>[g("div",Ye,[Qe,p.changeData.supplementary_material?(d(),k(x,{key:0,type:"success",size:"small"},{default:t(()=>[C("已上传")]),_:1})):(d(),k(x,{key:1,type:"info",size:"small"},{default:t(()=>[C("未上传")]),_:1}))])]),default:t(()=>[g("div",We,[p.changeData.supplementary_material?(d(),U("div",Ge,[a(_,null,{default:t(()=>[a(q)]),_:1}),g("span",He,N(e.getFileName(p.changeData.supplementary_material)),1)])):(d(),U("div",Ke,[a(_,null,{default:t(()=>[a(O)]),_:1}),Xe])),g("div",Ze,[p.changeData.supplementary_material?(d(),k(y,{key:0,type:"primary",size:"small",onClick:r[7]||(r[7]=l=>e.downloadFile("supplementary_material")),loading:e.downloadLoading.supplementary_material},{default:t(()=>[a(_,null,{default:t(()=>[a(z)]),_:1}),C(" 下载 ")]),_:1},8,["loading"])):T("",!0),a(E,{class:"upload-button",action:e.uploadUrl,headers:e.headers,data:e.getUploadData("supplementary_material"),"show-file-list":!1,"before-upload":e.beforeUpload,"http-request":l=>e.customUpload(l,"supplementary_material")},{default:t(()=>[a(y,{type:"success",size:"small",loading:e.uploadLoading.supplementary_material},{default:t(()=>[a(_,null,{default:t(()=>[a(O)]),_:1}),C(" "+N(p.changeData.supplementary_material?"重新上传":"上传"),1)]),_:1},8,["loading"])]),_:1},8,["action","headers","data","before-upload","http-request"]),p.changeData.supplementary_material?(d(),k(y,{key:1,type:"danger",size:"small",onClick:r[8]||(r[8]=l=>e.removeFile("supplementary_material")),loading:e.removeLoading.supplementary_material},{default:t(()=>[a(_,null,{default:t(()=>[a(c)]),_:1}),C(" 删除 ")]),_:1},8,["loading"])):T("",!0)])])]),_:1})]),_:1})]),_:1})])}const ea=K(he,[["render",$e],["__scopeId","data-v-d4460001"]]),aa={name:"ChangeManagementDetail",components:{FileAttachments:ea},setup(){const n=ie(),r=_e(),p=F(null),e=F(!1),u=G({id:null,change_id:"",title:"",system:"",change_level:"",planned_change_time:"",requester:"",implementers:"",oa_process:!1,oa_process_file:null,signed_archive:!1,signed_archive_file:null,operation_sheet:null,supplementary_material:null}),j=F([]),R=F([]),J=l=>{u.implementers=l.join(",")},_=l=>{u.system=l.join(",")},y=l=>{if(!l)return"未知用户";const S=L.value.find(o=>o.username===l);return S?S.real_name:l},I={title:[{required:!0,message:"请输入变更名称",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],system:[{required:!0,message:"请选择变更系统",trigger:"change"}],change_level:[{required:!0,message:"请选择变更级别",trigger:"change"}],planned_change_time:[{required:!0,message:"请选择计划变更时间",trigger:"change"}],requester:[{required:!0,message:"请选择变更负责人",trigger:"change"}],implementers:[{required:!0,message:"请选择变更实施人",trigger:"change"}]},V=F([]),z=F([]),L=F([]),x=async()=>{try{const l=await Y({url:"/api/get_system_list",method:"post"});l.code===0&&(V.value=l.msg)}catch(l){console.error("获取系统列表失败:",l),m.error("获取系统列表失败")}},q=async()=>{try{const l=await Y({url:"/api/get_cmdb_data_dictionary",method:"post",data:{dict_type:"P"}});l.code===0&&(z.value=l.msg)}catch(l){console.error("获取变更级别列表失败:",l),m.error("获取变更级别列表失败")}},O=async()=>{try{const l=await Y({url:"/api/get_user_list",method:"post"});l.code===0&&(L.value=l.msg)}catch(l){console.error("获取用户列表失败:",l),m.error("获取用户列表失败")}},E=async(l,S=!0,o=!0)=>{let i=null;S&&(i=de.service({lock:!0,text:"加载中...",background:"rgba(0, 0, 0, 0.7)"}));try{console.log("获取变更详情，ID:",l);const s=new Date().getTime(),v=await Y({url:"/api/get_ops_change_management",method:"post",data:{id:l,_t:s},headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}});if(console.log("获取变更详情响应:",v),v.code===0&&v.msg.length>0){const D=v.msg[0];console.log("获取到的变更数据:",D);const f={};return Object.keys(u).forEach(h=>{if(D[h]!==void 0)if(h==="planned_change_time"&&D[h]){const w=D[h];console.log("处理计划时间字段:",w,"类型:",typeof w),f[h]=ge(w),console.log("时区安全转换结果:",f[h])}else f[h]=D[h];else f[h]=u[h]}),console.log("更新前的数据:",JSON.stringify({oa_process:u.oa_process,oa_process_file:u.oa_process_file,signed_archive:u.signed_archive,signed_archive_file:u.signed_archive_file,operation_sheet:u.operation_sheet})),console.log("更新后的数据:",JSON.stringify({oa_process:f.oa_process,oa_process_file:f.oa_process_file,signed_archive:f.signed_archive,signed_archive_file:f.signed_archive_file,operation_sheet:f.operation_sheet})),Object.assign(u,f),u.implementers&&(j.value=u.implementers.split(",")),u.system&&(R.value=u.system.split(",")),o&&m.success("数据已刷新"),!0}else return o&&m.error("未找到变更记录"),r.push("/ops_change_management"),!1}catch(s){return console.error("获取变更详情失败:",s),o&&m.error("获取变更详情失败"),r.push("/ops_change_management"),!1}finally{i&&i.close()}},c=async()=>(console.log("刷新变更详情数据"),u.id?await E(u.id,!1,!0):(console.log("变更ID为空，无法刷新数据"),!1)),B=async()=>{p.value&&await p.value.validate(async l=>{if(!l){m.error("请检查表单填写是否正确");return}e.value=!0;try{const S=u.id?"/api/update_ops_change_management":"/api/add_ops_change_management",o=localStorage.getItem("loginUsername")||"admin",i={...u,changeLevel:u.change_level,plannedChangeTime:u.planned_change_time,usernameby:o,created_by:o,updated_by:o};console.log("发送请求数据:",i);const s=await Y({url:S,method:"post",data:i});s.code===0?(m.success("保存成功"),u.id||(u.id=s.msg.id,u.change_id=s.msg.change_id)):m.error(`保存失败: ${s.msg}`)}catch(S){console.error("保存变更失败:",S),m.error("保存变更失败")}finally{e.value=!1}})},Q=()=>{r.push("/ops_change_management")};return X(async()=>{await Promise.all([x(),q(),O()]);const l=n.params.id;if(l&&l!=="new")await E(l);else{const S=localStorage.getItem("username");S&&(u.requester=S)}}),{formRef:p,changeData:u,implementersArray:j,systemArray:R,rules:I,systemOptions:V,changeLevelOptions:z,userOptions:L,saveLoading:e,handleImplementersChange:J,handleSystemChange:_,getUserRealName:y,saveChange:B,goBack:Q,refreshChangeData:c}}},ee=n=>(Z("data-v-aaf207e2"),n=n(),$(),n),ta={class:"app-container"},oa={class:"card-header"},la=ee(()=>g("span",null,"变更详情",-1)),na={class:"header-buttons"},sa={class:"change-id-section"},ra={class:"change-id-text"},ca={class:"change-info-card"},ia=ee(()=>g("div",{class:"card-title"},"变更信息",-1)),da={class:"card-content"},_a={key:0,class:"tag-display"},ga={key:0,class:"tag-display"};function ha(n,r,p,e,u,j){const R=b("el-button"),J=b("el-input"),_=b("el-form-item"),y=b("el-col"),I=b("el-option"),V=b("el-select"),z=b("el-row"),L=b("el-date-picker"),x=b("el-tag"),q=b("el-form"),O=b("file-attachments"),E=b("el-card");return d(),U("div",ta,[a(E,{class:"box-card"},{header:t(()=>[g("div",oa,[la,g("div",na,[a(R,{type:"primary",onClick:e.goBack},{default:t(()=>[C("返回列表")]),_:1},8,["onClick"]),a(R,{type:"success",onClick:e.saveChange,loading:e.saveLoading},{default:t(()=>[C("保存")]),_:1},8,["onClick","loading"])])])]),default:t(()=>[g("div",sa,[g("span",ra,"变更编号："+N(e.changeData.change_id||"系统自动生成"),1)]),g("div",ca,[ia,g("div",da,[a(q,{ref:"formRef",model:e.changeData,rules:e.rules,"label-width":"100px","label-position":"right"},{default:t(()=>[a(z,{gutter:16},{default:t(()=>[a(y,{xs:24,sm:12,md:12,lg:12,xl:12},{default:t(()=>[a(_,{label:"变更名称",prop:"title"},{default:t(()=>[a(J,{modelValue:e.changeData.title,"onUpdate:modelValue":r[0]||(r[0]=c=>e.changeData.title=c),placeholder:"请输入变更名称",size:"small"},null,8,["modelValue"])]),_:1})]),_:1}),a(y,{xs:24,sm:12,md:12,lg:12,xl:12},{default:t(()=>[a(_,{label:"变更级别",prop:"change_level"},{default:t(()=>[a(V,{modelValue:e.changeData.change_level,"onUpdate:modelValue":r[1]||(r[1]=c=>e.changeData.change_level=c),filterable:"",placeholder:"请选择变更级别",style:{width:"100%"},size:"small"},{default:t(()=>[(d(!0),U(P,null,M(e.changeLevelOptions,c=>(d(),k(I,{key:c.dict_code,label:c.dict_name,value:c.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(z,{gutter:16},{default:t(()=>[a(y,{xs:24,sm:12,md:12,lg:12,xl:12},{default:t(()=>[a(_,{label:"计划时间",prop:"planned_change_time"},{default:t(()=>[a(L,{modelValue:e.changeData.planned_change_time,"onUpdate:modelValue":r[2]||(r[2]=c=>e.changeData.planned_change_time=c),type:"date",placeholder:"选择日期",style:{width:"100%"},"value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",size:"small"},null,8,["modelValue"])]),_:1})]),_:1}),a(y,{xs:24,sm:12,md:12,lg:12,xl:12},{default:t(()=>[a(_,{label:"变更负责人",prop:"requester"},{default:t(()=>[a(V,{modelValue:e.changeData.requester,"onUpdate:modelValue":r[3]||(r[3]=c=>e.changeData.requester=c),filterable:"",placeholder:"请选择负责人",style:{width:"100%"},size:"small"},{default:t(()=>[(d(!0),U(P,null,M(e.userOptions,c=>(d(),k(I,{key:c.username,label:c.real_name,value:c.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(z,{gutter:16},{default:t(()=>[a(y,{xs:24,sm:12,md:12,lg:12,xl:12},{default:t(()=>[a(_,{label:"变更系统",prop:"system"},{default:t(()=>[a(V,{modelValue:e.systemArray,"onUpdate:modelValue":r[4]||(r[4]=c=>e.systemArray=c),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",filterable:"",placeholder:"请选择变更系统",style:{width:"100%"},size:"small",onChange:e.handleSystemChange},{default:t(()=>[(d(!0),U(P,null,M(e.systemOptions,c=>(d(),k(I,{key:c.system_abbreviation,label:c.system_abbreviation,value:c.system_abbreviation},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"]),e.systemArray.length>0?(d(),U("div",_a,[(d(!0),U(P,null,M(e.systemArray,c=>(d(),k(x,{key:c,class:"display-tag",type:"primary",effect:"light",size:"small"},{default:t(()=>[C(N(c),1)]),_:2},1024))),128))])):T("",!0)]),_:1})]),_:1}),a(y,{xs:24,sm:12,md:12,lg:12,xl:12},{default:t(()=>[a(_,{label:"变更实施人",prop:"implementers"},{default:t(()=>[a(V,{modelValue:e.implementersArray,"onUpdate:modelValue":r[5]||(r[5]=c=>e.implementersArray=c),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",filterable:"",placeholder:"请选择实施人",style:{width:"100%"},size:"small",onChange:e.handleImplementersChange},{default:t(()=>[(d(!0),U(P,null,M(e.userOptions,c=>(d(),k(I,{key:c.username,label:c.real_name,value:c.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"]),e.implementersArray.length>0?(d(),U("div",ga,[(d(!0),U(P,null,M(e.implementersArray,c=>(d(),k(x,{key:c,class:"display-tag",type:"primary",effect:"light",size:"small"},{default:t(()=>[C(N(e.getUserRealName(c)),1)]),_:2},1024))),128))])):T("",!0)]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])])]),a(O,{changeData:e.changeData,"onUpdate:changeData":r[6]||(r[6]=c=>e.changeData=c),refreshChangeData:e.refreshChangeData},null,8,["changeData","refreshChangeData"])]),_:1})])}const pa=K(aa,[["render",ha],["__scopeId","data-v-aaf207e2"]]);export{pa as default};
