import{d as S,r as b,o as D,g as E,E as y,c as C,a as t,w as s,b as c,e as m,f as a,t as n,h as l,n as h,u as p,i as N,s as V,j as F,k as $,F as j,l as q,m as z,p as L,q as P,_ as T}from"./index-BnaD8Tdo.js";const r=_=>(L("data-v-64691491"),_=_(),P(),_),A={class:"dashboard"},H={class:"card-header"},J=r(()=>a("span",null,"资产总数",-1)),K={class:"card-body"},O={class:"number"},Q={class:"compare"},R={class:"card-header"},U=r(()=>a("span",null,"配置项",-1)),W={class:"card-body"},X={class:"number"},Y={class:"compare"},Z={class:"card-header"},aa=r(()=>a("span",null,"变更数",-1)),sa={class:"card-body"},ta={class:"number"},ea={class:"compare"},oa={class:"card-header"},na=r(()=>a("span",null,"告警数",-1)),ca={class:"card-body"},da={class:"number"},la={class:"compare"},ra=r(()=>a("div",{class:"card-header"},[a("span",null,"最近活动")],-1)),_a=S({__name:"dashboard",setup(_){const e=b({assetCount:1234,assetGrowth:5.2,configCount:456,configGrowth:-2.1,changeCount:89,changeGrowth:12.5,alertCount:3,alertGrowth:-50}),g=b([]),v=E(),w=v==null?void 0:v.appContext.config.globalProperties.$axios;D(()=>{G(),x()});async function G(){try{const o=await w.post("/api/get_cmdb_dashboard");e.value.assetCount=o.data.msg[0].asset_count,e.value.assetGrowth=o.data.msg[0].asset_growth,e.value.configCount=o.data.msg[0].config_count,e.value.configGrowth=o.data.msg[0].config_growth}catch(o){console.error("数据加载失败:",o),y.error("数据加载失败")}}async function x(){try{const o=await w.post("/api/get_cmdb_recent_activity");g.value=o.data.msg}catch(o){console.error("数据加载失败:",o),y.error("数据加载失败")}}return(o,ia)=>{const i=c("el-icon"),d=c("el-card"),u=c("el-col"),M=c("el-row"),k=c("el-timeline-item"),I=c("el-timeline");return m(),C("div",A,[t(M,{gutter:10},{default:s(()=>[t(u,{span:6},{default:s(()=>[t(d,{shadow:"hover",class:"stat-card"},{header:s(()=>[a("div",H,[J,t(i,null,{default:s(()=>[t(p(N))]),_:1})])]),default:s(()=>[a("div",K,[a("div",O,n(e.value.assetCount),1),a("div",Q,[l(" 较上月 "),a("span",{class:h(e.value.assetGrowth>=0?"up":"down")},n(Math.abs(e.value.assetGrowth))+"% ",3)])])]),_:1})]),_:1}),t(u,{span:6},{default:s(()=>[t(d,{shadow:"hover",class:"stat-card"},{header:s(()=>[a("div",R,[U,t(i,null,{default:s(()=>[t(p(V))]),_:1})])]),default:s(()=>[a("div",W,[a("div",X,n(e.value.configCount),1),a("div",Y,[l(" 较上月 "),a("span",{class:h(e.value.configGrowth>=0?"up":"down")},n(Math.abs(e.value.configGrowth))+"% ",3)])])]),_:1})]),_:1}),t(u,{span:6},{default:s(()=>[t(d,{shadow:"hover",class:"stat-card"},{header:s(()=>[a("div",Z,[aa,t(i,null,{default:s(()=>[t(p(F))]),_:1})])]),default:s(()=>[a("div",sa,[a("div",ta,n(e.value.changeCount),1),a("div",ea,[l(" 较上月 "),a("span",{class:h(e.value.changeGrowth>=0?"up":"down")},n(Math.abs(e.value.changeGrowth))+"% ",3)])])]),_:1})]),_:1}),t(u,{span:6},{default:s(()=>[t(d,{shadow:"hover",class:"stat-card"},{header:s(()=>[a("div",oa,[na,t(i,null,{default:s(()=>[t(p($))]),_:1})])]),default:s(()=>[a("div",ca,[a("div",da,n(e.value.alertCount),1),a("div",la,[l(" 较上月 "),a("span",{class:h(e.value.alertGrowth>=0?"up":"down")},n(Math.abs(e.value.alertGrowth))+"% ",3)])])]),_:1})]),_:1})]),_:1}),t(d,{shadow:"hover",class:"activity-card"},{header:s(()=>[ra]),default:s(()=>[t(I,null,{default:s(()=>[(m(!0),C(j,null,q(g.value,(f,B)=>(m(),z(k,{key:B,timestamp:f.activity_time,type:f.activity_type},{default:s(()=>[l(n(f.activity_info),1)]),_:2},1032,["timestamp","type"]))),128))]),_:1})]),_:1})])}}}),ha=T(_a,[["__scopeId","data-v-64691491"]]);export{ha as default};
