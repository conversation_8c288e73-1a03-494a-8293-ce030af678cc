import{_ as E,z as N,v as O,A as F,c as T,a as e,f as i,w as a,b as r,h as c,B as j,C as q,m as G,x as J,t as K,p as L,q as Q,e as v}from"./index-BnaD8Tdo.js";import{u as V,w as R,F as W}from"./FileSaver.min-CkyCPK_c.js";const X={components:{Plus:F,Search:O,Download:N},data(){var o,t,f;return{userArr:[],loading:!1,hasDeletePermission:(o=localStorage.getItem("role_code"))==null?void 0:o.includes("D"),hasUpdatePermission:(t=localStorage.getItem("role_code"))==null?void 0:t.includes("U"),hasInsertPermission:(f=localStorage.getItem("role_code"))==null?void 0:f.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{ip_address:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,ip_address:"",description:"",status:"正常",last_checked:""}}},mounted(){this.loadData()},methods:{handlePageChange(o){this.search.currentPage=o,this.loadData()},handlePageSizeChange(o){this.search.pageSize=o,this.search.currentPage=1,this.loadData()},handleSortChange({prop:o,order:t}){this.search.sortProp=o,this.search.sortOrder=t==="ascending"?"asc":"desc",this.loadData()},resetSearch(){this.search={ip_address:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},async loadData(){try{this.loading=!0;const o=await this.$axios.post("/api/get_cmdb_monitored_ip_list",this.search);this.userArr=o.data.msg,this.search.total=o.data.total}catch(o){console.error("数据加载失败:",o),this.$message.error("数据加载失败")}finally{this.loading=!1}},async submitAdd(){try{await this.$axios.post("/api/add_cmdb_monitored_ip_list",this.formData),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(o){console.error("添加失败:",o),this.$message.error("添加失败")}},async submitEdit(){try{await this.$axios.post("/api/update_cmdb_monitored_ip_list",this.formData),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(o){console.error("更新失败:",o),this.$message.error("更新失败")}},async submitDelete(){try{await this.$axios.post("/api/del_cmdb_monitored_ip_list",this.formData),this.$message.success("删除成功"),this.loadData(),this.dialogVisible.delete=!1}catch(o){console.error("删除失败:",o),this.$message.error("删除失败")}},handleAdd(){this.dialogVisible.add=!this.dialogVisible.add,this.formData={}},handleEdit(o,t){this.dialogVisible.edit=!0,this.formData.id=t.id,this.formData.ip_address=t.ip_address,this.formData.description=t.description,this.formData.last_checked=t.last_checked},handleDelete(o,t){this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=t.id,this.formData.ip_address=t.ip_address},exportData(){try{const o=this.$refs.table;if(!o){this.$message.error("表格实例不存在");return}const t=o.columns.filter(m=>m.visible!==!1);if(t.length===0){this.$message.error("没有可导出的列");return}const f=t.map(m=>m.label);if(!this.userArr||!Array.isArray(this.userArr)){this.$message.error("没有可导出的数据");return}const y=this.userArr.map(m=>t.map(n=>m[n.property]||"")),l=[f,...y],d=V.aoa_to_sheet(l),u=V.book_new();V.book_append_sheet(u,d,"Sheet1");const h=R(u,{bookType:"xlsx",type:"array"}),b=new Blob([h],{type:"application/octet-stream"});W.saveAs(b,"监控IP列表.xlsx")}catch(o){console.error("导出数据时发生错误:",o),this.$message.error("导出数据失败: "+o.message)}}}},_=o=>(L("data-v-f9cc0285"),o=o(),Q(),o),Z={class:"user-manage"},$={class:"dialogdiv"},ee=_(()=>i("span",{class:"label"},"IP地址:",-1)),te=_(()=>i("span",{class:"label"},"描述:",-1)),le=_(()=>i("span",{class:"label"},"状态:",-1)),ae=_(()=>i("span",{class:"label"},"最后检查时间:",-1)),se={class:"dialog-footer"},oe={class:"dialogdiv"},ie=_(()=>i("span",{class:"label"},"IP地址:",-1)),re=_(()=>i("span",{class:"label"},"描述:",-1)),de=_(()=>i("span",{class:"label"},"状态:",-1)),ne=_(()=>i("span",{class:"label"},"最后检查时间:",-1)),ce={class:"dialog-footer"},pe={class:"button-container"},ue={class:"action-bar unified-action-bar"},me={class:"action-bar-left"},_e={class:"action-bar-right"},he={style:{display:"flex","white-space":"nowrap"}},fe={class:"pagination"};function be(o,t,f,y,l,d){const u=r("el-input"),h=r("el-option"),b=r("el-select"),m=r("el-date-picker"),n=r("el-button"),g=r("el-dialog"),x=r("el-alert"),k=r("el-form-item"),w=r("el-col"),P=r("Search"),D=r("el-icon"),S=r("el-row"),I=r("el-form"),C=r("el-card"),Y=r("Plus"),z=r("Download"),p=r("el-table-column"),U=r("el-tag"),A=r("el-table"),B=r("el-pagination"),H=q("loading");return v(),T("div",Z,[e(g,{modelValue:l.dialogVisible.add,"onUpdate:modelValue":t[5]||(t[5]=s=>l.dialogVisible.add=s),title:"添加监控IP",width:"400","align-center":""},{footer:a(()=>[i("div",se,[e(n,{onClick:t[4]||(t[4]=s=>l.dialogVisible.add=!1)},{default:a(()=>[c("返回")]),_:1}),e(n,{type:"primary",onClick:d.submitAdd},{default:a(()=>[c("确定")]),_:1},8,["onClick"])])]),default:a(()=>[i("div",$,[i("p",null,[ee,e(u,{modelValue:l.formData.ip_address,"onUpdate:modelValue":t[0]||(t[0]=s=>l.formData.ip_address=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),i("p",null,[te,e(u,{modelValue:l.formData.description,"onUpdate:modelValue":t[1]||(t[1]=s=>l.formData.description=s),style:{width:"240px"}},null,8,["modelValue"])]),i("p",null,[le,e(b,{modelValue:l.formData.status,"onUpdate:modelValue":t[2]||(t[2]=s=>l.formData.status=s),style:{width:"240px"},placeholder:"请选择状态"},{default:a(()=>[e(h,{label:"正常",value:"正常"}),e(h,{label:"异常",value:"异常"})]),_:1},8,["modelValue"])]),i("p",null,[ae,e(m,{modelValue:l.formData.last_checked,"onUpdate:modelValue":t[3]||(t[3]=s=>l.formData.last_checked=s),type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY-MM-DD HH:mm:ss",style:{width:"240px"},placeholder:"选择日期时间"},null,8,["modelValue"])])])]),_:1},8,["modelValue"]),e(g,{modelValue:l.dialogVisible.edit,"onUpdate:modelValue":t[11]||(t[11]=s=>l.dialogVisible.edit=s),title:"编辑监控IP",width:"400","align-center":""},{footer:a(()=>[i("div",ce,[e(n,{onClick:t[10]||(t[10]=s=>l.dialogVisible.edit=!1)},{default:a(()=>[c("取消")]),_:1}),e(n,{type:"primary",onClick:d.submitEdit},{default:a(()=>[c("更新")]),_:1},8,["onClick"])])]),default:a(()=>[i("div",oe,[i("p",null,[ie,e(u,{modelValue:l.formData.ip_address,"onUpdate:modelValue":t[6]||(t[6]=s=>l.formData.ip_address=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),i("p",null,[re,e(u,{modelValue:l.formData.description,"onUpdate:modelValue":t[7]||(t[7]=s=>l.formData.description=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),i("p",null,[de,e(b,{modelValue:l.formData.status,"onUpdate:modelValue":t[8]||(t[8]=s=>l.formData.status=s),style:{width:"240px"}},{default:a(()=>[e(h,{label:"正常",value:"正常"}),e(h,{label:"异常",value:"异常"})]),_:1},8,["modelValue"])]),i("p",null,[ne,e(m,{modelValue:l.formData.last_checked,"onUpdate:modelValue":t[9]||(t[9]=s=>l.formData.last_checked=s),type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY-MM-DD HH:mm:ss",style:{width:"240px"},placeholder:"选择日期时间"},null,8,["modelValue"])])])]),_:1},8,["modelValue"]),e(g,{modelValue:l.dialogVisible.delete,"onUpdate:modelValue":t[13]||(t[13]=s=>l.dialogVisible.delete=s),title:"删除监控IP",width:"500","align-center":""},{footer:a(()=>[i("div",null,[e(n,{onClick:t[12]||(t[12]=s=>l.dialogVisible.delete=!1)},{default:a(()=>[c("取消")]),_:1}),e(n,{type:"danger",onClick:d.submitDelete},{default:a(()=>[c("确认删除")]),_:1},8,["onClick"])])]),default:a(()=>[e(x,{type:"warning",title:`确定要删除 IP 为 ${l.formData.ip_address} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),e(C,{class:"search-card"},{default:a(()=>[e(I,{inline:!0},{default:a(()=>[e(S,{gutter:10},{default:a(()=>[e(w,{xs:24,sm:12,md:6,lg:6},{default:a(()=>[e(k,{label:"IP地址",class:"form-item-with-label"},{default:a(()=>[e(u,{modelValue:l.search.ip_address,"onUpdate:modelValue":t[14]||(t[14]=s=>l.search.ip_address=s),placeholder:"请输入IP地址",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(w,{xs:24,sm:12,md:18,lg:18,class:"search-buttons-col"},{default:a(()=>[e(k,{label:" ",class:"form-item-with-label search-buttons"},{default:a(()=>[i("div",pe,[e(n,{type:"primary",onClick:d.loadData},{default:a(()=>[e(D,null,{default:a(()=>[e(P)]),_:1}),c("查询 ")]),_:1},8,["onClick"]),e(n,{onClick:d.resetSearch},{default:a(()=>[c("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),i("div",ue,[i("div",me,[e(n,{type:"success",disabled:!l.hasInsertPermission,onClick:d.handleAdd},{default:a(()=>[e(D,null,{default:a(()=>[e(Y)]),_:1}),c("新增监控IP ")]),_:1},8,["disabled","onClick"])]),i("div",_e,[e(n,{type:"info",onClick:d.exportData},{default:a(()=>[e(D,null,{default:a(()=>[e(z)]),_:1}),c(" 导出数据 ")]),_:1},8,["onClick"])])]),e(C,{class:"table-card"},{default:a(()=>[j((v(),G(A,{data:l.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:d.handleSortChange},{default:a(()=>[J("",!0),e(p,{prop:"ip_address",label:"IP地址",sortable:""}),e(p,{prop:"description",label:"描述",sortable:""}),e(p,{prop:"status",label:"状态",sortable:""},{default:a(s=>[e(U,{type:s.row.status==="正常"?"success":"danger"},{default:a(()=>[c(K(s.row.status),1)]),_:2},1032,["type"])]),_:1}),e(p,{prop:"last_checked",label:"最后检查时间",sortable:""}),e(p,{prop:"created_at",label:"创建时间",sortable:""}),e(p,{prop:"created_by",label:"创建人",sortable:""}),e(p,{prop:"updated_at",label:"更新时间",sortable:""}),e(p,{prop:"updated_by",label:"更新人",sortable:""}),e(p,{label:"操作",width:"140",align:"center",fixed:"right"},{default:a(s=>[i("div",he,[e(n,{size:"small",type:"warning",disabled:!l.hasUpdatePermission,onClick:M=>d.handleEdit(s.$index,s.row)},{default:a(()=>[c("编辑")]),_:2},1032,["disabled","onClick"]),e(n,{size:"small",type:"danger",disabled:!l.hasDeletePermission,onClick:M=>d.handleDelete(s.$index,s.row)},{default:a(()=>[c("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[H,l.loading]]),i("div",fe,[e(B,{background:"","current-page":l.search.currentPage,"page-size":l.search.pageSize,total:l.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:d.handlePageSizeChange,onCurrentChange:d.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const Ve=E(X,[["render",be],["__scopeId","data-v-f9cc0285"]]);export{Ve as default};
