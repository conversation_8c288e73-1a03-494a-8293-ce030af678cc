#!/bin/bash

# CMDB系统405错误修复脚本
# 适用于主服务器和备用服务器
# 使用方法：./fix_405_error.sh [primary|backup]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -eq 0 ]; then
    log_error "请指定服务器类型: primary 或 backup"
    echo "使用方法: $0 [primary|backup]"
    exit 1
fi

SERVER_TYPE=$1

# 根据服务器类型设置变量
if [ "$SERVER_TYPE" = "primary" ]; then
    SERVER_IP="*************"
    PROJECT_PATH="/opt/cmdb_v2.0"
    NGINX_CONF_PATH="/etc/nginx/conf.d/cmdb_129.81.conf"
    FIXED_CONF_PATH="/opt/cmdb_v2.0/frontend/nginx_config/cmdb_129.81_fixed.conf"
    BACKEND_PATH="/opt/cmdb_v2.0/backend"
    FRONTEND_PATH="/opt/cmdb_v2.0/frontend"
elif [ "$SERVER_TYPE" = "backup" ]; then
    SERVER_IP="*************"
    PROJECT_PATH="/home/<USER>/cmdb_v2.0"
    NGINX_CONF_PATH="/etc/nginx/conf.d/cmdb_129.82.conf"
    FIXED_CONF_PATH="/home/<USER>/cmdb_v2.0/frontend/nginx_config/cmdb_129.82_fixed.conf"
    BACKEND_PATH="/home/<USER>/cmdb_v2.0/backend"
    FRONTEND_PATH="/home/<USER>/cmdb_v2.0/frontend"
else
    log_error "无效的服务器类型: $SERVER_TYPE"
    echo "请使用 'primary' 或 'backup'"
    exit 1
fi

log_info "开始修复 $SERVER_TYPE 服务器 ($SERVER_IP) 的405错误和静态资源404问题..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    log_error "请使用root权限运行此脚本"
    exit 1
fi

# 步骤1: 备份当前nginx配置
log_info "步骤1: 备份当前nginx配置..."
if [ -f "$NGINX_CONF_PATH" ]; then
    cp "$NGINX_CONF_PATH" "${NGINX_CONF_PATH}.backup.$(date +%Y%m%d_%H%M%S)"
    log_success "nginx配置已备份"
else
    log_warning "nginx配置文件不存在: $NGINX_CONF_PATH"
fi

# 步骤2: 检查修复配置文件是否存在
log_info "步骤2: 检查修复配置文件..."
if [ ! -f "$FIXED_CONF_PATH" ]; then
    log_error "修复配置文件不存在: $FIXED_CONF_PATH"
    log_info "请确保已从代码仓库拉取最新代码"
    exit 1
fi
log_success "修复配置文件存在"

# 步骤3: 应用修复配置
log_info "步骤3: 应用修复配置..."
cp "$FIXED_CONF_PATH" "$NGINX_CONF_PATH"
log_success "修复配置已应用"

# 步骤4: 测试nginx配置
log_info "步骤4: 测试nginx配置..."
if nginx -t; then
    log_success "nginx配置测试通过"
else
    log_error "nginx配置测试失败，恢复备份配置"
    cp "${NGINX_CONF_PATH}.backup.$(date +%Y%m%d_%H%M%S)" "$NGINX_CONF_PATH"
    exit 1
fi

# 步骤5: 重载nginx
log_info "步骤5: 重载nginx..."
if nginx -s reload; then
    log_success "nginx重载成功"
else
    log_error "nginx重载失败"
    exit 1
fi

# 步骤6: 重启后端服务（如果是主服务器）
if [ "$SERVER_TYPE" = "primary" ]; then
    log_info "步骤6: 重启后端服务..."
    cd "$BACKEND_PATH"
    if command -v pm2 >/dev/null 2>&1; then
        pm2 restart cmdb-backend || pm2 start index.js --name cmdb-backend
        log_success "后端服务重启成功"
    else
        log_warning "PM2未安装，请手动重启后端服务"
    fi
fi

# 步骤7: 验证修复效果
log_info "步骤7: 验证修复效果..."

# 检查健康检查端点
log_info "检查健康检查端点..."
if curl -s -I "http://$SERVER_IP:9000/health" | grep -q "200 OK"; then
    log_success "健康检查端点正常"
else
    log_warning "健康检查端点异常"
fi

# 检查API测试端点
log_info "检查API测试端点..."
if curl -s -I "http://$SERVER_IP:9000/api/test_method" | grep -q "X-"; then
    log_success "API测试端点正常，调试头信息已添加"
else
    log_warning "API测试端点可能异常"
fi

# 步骤8: 显示验证命令
log_info "步骤8: 手动验证建议..."
echo ""
log_info "请执行以下命令进行进一步验证："
echo "1. 检查nginx状态:"
echo "   systemctl status nginx"
echo ""
echo "2. 检查后端日志:"
if [ "$SERVER_TYPE" = "primary" ]; then
    echo "   pm2 logs cmdb-backend"
fi
echo ""
echo "3. 访问调试页面:"
echo "   http://$SERVER_IP:9000/debug_api_test"
echo "   http://$SERVER_IP:9000/static_resource_test"
echo ""
echo "4. 检查API响应头:"
echo "   curl -I http://$SERVER_IP:9000/api/test_method"
echo ""

log_success "405错误修复脚本执行完成！"
log_info "如果问题仍然存在，请查看详细的修复指南: docs/production_405_error_fix.md"
