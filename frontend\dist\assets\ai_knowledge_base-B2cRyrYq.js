import{_ as g,a1 as k,a2 as w,c,f as e,a as o,w as n,h as L,b as t,x as b,p as T,q as v,e as r}from"./index-BnaD8Tdo.js";const I={name:"KnowledgeBase",components:{ArrowLeft:w,Loading:k},data(){return{loading:!0,iframeUrl:"http://10.242.194.92/chatbot/ahYpukPLRrh8QO4e",loadTimeout:null}},mounted(){this.loadTimeout=setTimeout(()=>{this.loading=!1},1e4),this.$message({message:"正在加载知识库，请稍候...",type:"info",duration:3e3})},beforeUnmount(){this.loadTimeout&&clearTimeout(this.loadTimeout)},methods:{goBack(){this.$router.push("/ai_platform")},handleIframeLoaded(){this.loading=!1,this.loadTimeout&&clearTimeout(this.loadTimeout),this.$message({message:"知识库加载完成",type:"success",duration:2e3})}}},_=a=>(T("data-v-c2baeef9"),a=a(),v(),a),x={class:"knowledge-base"},B={class:"knowledge-header"},y=_(()=>e("h2",{class:"page-title"},"知识海洋（知识库）",-1)),C={class:"header-actions"},A={class:"knowledge-content"},N={key:0,class:"loading-container"},V={class:"loading-text"},S=_(()=>e("span",null,"知识库加载中，请稍候...",-1)),U=["src"];function $(a,d,q,E,i,s){const m=t("ArrowLeft"),l=t("el-icon"),h=t("el-button"),u=t("el-skeleton"),f=t("Loading");return r(),c("div",x,[e("div",B,[y,e("div",C,[o(h,{type:"primary",onClick:s.goBack},{default:n(()=>[o(l,null,{default:n(()=>[o(m)]),_:1}),L(" 返回AI平台 ")]),_:1},8,["onClick"])])]),e("div",A,[i.loading?(r(),c("div",N,[o(u,{rows:6,animated:""}),e("div",V,[o(l,{class:"loading-icon"},{default:n(()=>[o(f)]),_:1}),S])])):b("",!0),e("iframe",{ref:"knowledgeFrame",src:i.iframeUrl,frameborder:"0",allow:"microphone",onLoad:d[0]||(d[0]=(...p)=>s.handleIframeLoaded&&s.handleIframeLoaded(...p))},`
      `,40,U)])])}const K=g(I,[["render",$],["__scopeId","data-v-c2baeef9"]]);export{K as default};
