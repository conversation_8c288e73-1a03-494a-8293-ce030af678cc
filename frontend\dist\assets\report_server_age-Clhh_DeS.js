import{_ as z,Z as q,A as W,U as Z,$ as j,z as G,c as m,f as _,a as l,w as s,x,B as H,m as g,F as y,l as C,h as u,t as w,b as d,C as K,E as n,p as Q,q as X,e as r,n as $}from"./index-BnaD8Tdo.js";import{u as U,w as ee,F as te}from"./FileSaver.min-CkyCPK_c.js";const ae={name:"report_server_age",components:{Download:G,Filter:j,Delete:Z,Plus:W,Star:q},data(){const e=JSON.parse(localStorage.getItem("serverAgeState")||"null");return{deviceAgeData:(e==null?void 0:e.deviceAgeData)||[],loading:!1,filterDialogVisible:!1,favoritesDialogVisible:!1,dataCenterOptions:[],operationStatusOptions:[],innovativeTechOptions:[],selectedDataCenters:(e==null?void 0:e.selectedDataCenters)||[],selectedOperationStatuses:(e==null?void 0:e.selectedOperationStatuses)||[],selectedInnovativeTechFlag:(e==null?void 0:e.selectedInnovativeTechFlag)||"",timePeriods:(e==null?void 0:e.timePeriods)||[],filterForm:{dataCenters:[],operationStatuses:[],innovativeTechFlag:"",timePeriods:[{startDate:null,endDate:null,label:""}]},favoriteForm:{name:""},filterFavorites:[],restoredFromStorage:!!e}},computed:{hasActiveFilters(){return this.selectedDataCenters.length>0||this.selectedOperationStatuses.length>0||this.selectedInnovativeTechFlag!==""||this.timePeriods.length>0}},mounted(){this.loadDataCenters(),this.loadOperationStatuses(),this.loadInnovativeTechFlags(),this.loadFilterFavorites(),this.restoredFromStorage?this.deviceAgeData.length===0&&(this.selectedDataCenters.length>0||this.selectedOperationStatuses.length>0||this.selectedInnovativeTechFlag!==""||this.timePeriods.length>0)&&this.loadDeviceAgeData():this.loadDeviceAgeData()},methods:{async loadDataCenters(){try{const e=await this.$axios.post("/api/get_all_server_data_centers");e.data.code===0?this.dataCenterOptions=e.data.msg:n.error("机房数据加载失败")}catch(e){console.error("机房数据加载失败:",e),n.error("机房数据加载失败")}},async loadOperationStatuses(){try{const e=await this.$axios.post("/api/get_all_server_operation_statuses");e.data.code===0?this.operationStatusOptions=e.data.msg:n.error("运行状态数据加载失败")}catch(e){console.error("运行状态数据加载失败:",e),n.error("运行状态数据加载失败")}},async loadInnovativeTechFlags(){try{const e=await this.$axios.post("/api/get_all_server_innovative_tech_flags");e.data.code===0?this.innovativeTechOptions=e.data.msg:n.error("是否信创选项加载失败")}catch(e){console.error("是否信创选项加载失败:",e),n.error("是否信创选项加载失败")}},async loadDeviceAgeData(){try{this.loading=!0;const e={dataCenters:this.selectedDataCenters.length>0?this.selectedDataCenters:null,operationStatuses:this.selectedOperationStatuses.length>0?this.selectedOperationStatuses:null,innovativeTechFlag:this.selectedInnovativeTechFlag||null,timePeriods:this.timePeriods.length>0?this.timePeriods:null},t=await this.$axios.post("/api/get_server_age_statistics",e);t.data.code===0?(this.deviceAgeData=t.data.msg,this.saveStateToLocalStorage()):n.error("数据加载失败")}catch(e){console.error("数据加载失败:",e),n.error("数据加载失败")}finally{this.loading=!1}},saveStateToLocalStorage(){const e={deviceAgeData:this.deviceAgeData,selectedDataCenters:this.selectedDataCenters,selectedOperationStatuses:this.selectedOperationStatuses,selectedInnovativeTechFlag:this.selectedInnovativeTechFlag,timePeriods:this.timePeriods};localStorage.setItem("serverAgeState",JSON.stringify(e))},showFilterDialog(){this.filterForm.dataCenters=[...this.selectedDataCenters],this.filterForm.operationStatuses=[...this.selectedOperationStatuses],this.filterForm.innovativeTechFlag=this.selectedInnovativeTechFlag,this.timePeriods.length>0?this.filterForm.timePeriods=JSON.parse(JSON.stringify(this.timePeriods)):this.filterForm.timePeriods=[{startDate:null,endDate:null,label:""}],this.filterDialogVisible=!0},addTimePeriod(){this.filterForm.timePeriods.length<5&&this.filterForm.timePeriods.push({startDate:null,endDate:null,label:""})},removeFilterTimePeriod(e){this.filterForm.timePeriods.length>1&&this.filterForm.timePeriods.splice(e,1)},removeDataCenter(e){const t=this.selectedDataCenters.indexOf(e);t!==-1&&(this.selectedDataCenters.splice(t,1),this.loadDeviceAgeData())},removeOperationStatus(e){const t=this.selectedOperationStatuses.indexOf(e);t!==-1&&(this.selectedOperationStatuses.splice(t,1),this.loadDeviceAgeData())},removeInnovativeTechFlag(){this.selectedInnovativeTechFlag="",this.loadDeviceAgeData()},removePurchaseDateRange(e){const t=this.selectedPurchaseDateRanges.indexOf(e);t!==-1&&(this.selectedPurchaseDateRanges.splice(t,1),e==="custom"&&(this.timePeriods=[]),this.loadDeviceAgeData())},removeTimePeriod(e){this.timePeriods.splice(e,1),this.loadDeviceAgeData()},clearAllFilters(){this.selectedDataCenters=[],this.selectedOperationStatuses=[],this.selectedInnovativeTechFlag="",this.selectedPurchaseDateRanges=[],this.timePeriods=[],this.loadDeviceAgeData(),localStorage.removeItem("serverAgeState")},applyFilters(){const e=this.filterForm.timePeriods.filter(t=>t.startDate&&t.endDate);e.forEach(t=>{if(!t.label){const p=t.startDate?new Date(t.startDate).getFullYear():"",v=t.endDate?new Date(t.endDate).getFullYear():"";p===v?t.label=`${p}年`:t.label=`${p}-${v}年`}}),this.selectedDataCenters=[...this.filterForm.dataCenters],this.selectedOperationStatuses=[...this.filterForm.operationStatuses],this.selectedInnovativeTechFlag=this.filterForm.innovativeTechFlag,this.timePeriods=e,this.filterDialogVisible=!1,this.loadDeviceAgeData()},formatDate(e){if(!e)return"";if(typeof e=="string"&&e.match(/^\d{4}-\d{2}-\d{2}$/))return e;try{const t=new Date(e);if(isNaN(t.getTime()))return e;const p=t.getFullYear(),v=String(t.getMonth()+1).padStart(2,"0"),o=String(t.getDate()).padStart(2,"0");return`${p}-${v}-${o}`}catch(t){return console.error("Date formatting error:",t),e}},getPurchaseDateRangeLabel(e){switch(e){case"before_2016":return"2016年以前";case"between_2017_2019":return"2017-2019年";case"after_2020":return"2020年至今";case"custom":return"自定义时间段";default:return e}},getPercentageClass(e,t=!1){return e=parseFloat(e),t?e>=70?"percentage-good":e>=40?"percentage-medium":"percentage-bad":e<=10?"percentage-good":e<=30?"percentage-medium":"percentage-bad"},async loadFilterFavorites(){try{const e=localStorage.getItem("loginUsername")||"";if(!e){console.log("未找到登录用户名");return}const t=await this.$axios.post("/api/get_filter_favorites",{username:e});t.data.code===0?this.filterFavorites=t.data.msg:n.error("加载收藏列表失败")}catch(e){console.error("加载收藏列表失败:",e),n.error("加载收藏列表失败")}},async saveFilterFavorite(){try{const e=localStorage.getItem("loginUsername")||"";if(!e){n.warning("请先登录再保存收藏");return}if(!this.favoriteForm.name){n.warning("请输入收藏名称");return}const t={name:this.favoriteForm.name,dataCenters:this.filterForm.dataCenters,operationStatuses:this.filterForm.operationStatuses,innovativeTechFlag:this.filterForm.innovativeTechFlag,timePeriods:this.filterForm.timePeriods.filter(v=>v.startDate&&v.endDate),username:e};(await this.$axios.post("/api/save_filter_favorite",t)).data.code===0?(n.success("收藏成功"),this.favoriteForm.name="",this.loadFilterFavorites()):n.error("收藏失败")}catch(e){console.error("收藏失败:",e),n.error("收藏失败")}},async deleteFilterFavorite(e){try{const t=localStorage.getItem("loginUsername")||"";if(!t){n.warning("请先登录再进行操作");return}(await this.$axios.post("/api/delete_filter_favorite",{id:e,username:t})).data.code===0?(n.success("删除成功"),this.loadFilterFavorites()):n.error("删除失败")}catch(t){console.error("删除收藏失败:",t),n.error("删除失败")}},applyFilterFavorite(e){this.filterForm.dataCenters=[...e.data_centers],this.filterForm.operationStatuses=[...e.operation_statuses],this.filterForm.innovativeTechFlag=e.innovative_tech_flag||"",e.time_periods&&e.time_periods.length>0?this.filterForm.timePeriods=JSON.parse(JSON.stringify(e.time_periods)):this.filterForm.timePeriods=[{startDate:null,endDate:null,label:""}],this.favoritesDialogVisible&&(this.favoritesDialogVisible=!1,this.filterDialogVisible=!0)},showFavoritesDialog(){this.loadFilterFavorites(),this.favoritesDialogVisible=!0},exportData(){if(!this.$refs.deviceAgeTable)return;const t=["机房","全部设备数量"],p=["",""];this.timePeriods.forEach(c=>{const b=c.label||this.formatDate(c.startDate)+" 至 "+this.formatDate(c.endDate);t.push(b,""),p.push("数量","占比")});const v=[t,p],o=this.deviceAgeData.map(c=>{const b=[c.data_center,c.total_count];return this.timePeriods.forEach((O,S)=>{b.push(c["period_"+S+"_count"],c["period_"+S+"_percentage"]+"%")}),b}),i=[...v,...o],V=U.aoa_to_sheet(i),F=[{s:{r:0,c:0},e:{r:1,c:0}},{s:{r:0,c:1},e:{r:1,c:1}}];this.timePeriods.forEach((c,b)=>{const O=2+b*2;F.push({s:{r:0,c:O},e:{r:0,c:O+1}})}),V["!merges"]=F;const f=[{wch:20},{wch:12}];this.timePeriods.forEach(()=>{f.push({wch:10},{wch:10})}),V["!cols"]=f;const T=U.book_new();U.book_append_sheet(T,V,"实体服务器年限情况统计");const I=ee(T,{bookType:"xlsx",type:"array"}),P=new Blob([I],{type:"application/octet-stream"});te.saveAs(P,"实体服务器年限情况统计.xlsx")}}},A=e=>(Q("data-v-cf99c4f3"),e=e(),X(),e),le={class:"report-center"},se={class:"report-container"},oe={class:"card-header"},re=A(()=>_("h3",{class:"report-title"},"实体服务器年限情况统计",-1)),ie={class:"unified-action-bar"},ne={class:"action-bar-left"},ce={class:"action-bar-right"},de={key:0,class:"filter-tags"},he=A(()=>_("span",{class:"filter-label"},"当前筛选条件：",-1)),ue={class:"table-container"},me=A(()=>_("span",{class:"date-separator"},"至",-1)),fe=A(()=>_("span",{class:"date-separator"},"标签:",-1)),_e={class:"add-period-row"},pe={key:0,class:"period-limit-hint"},ge={class:"favorite-row"},ve={class:"favorites-list"},De={class:"dialog-footer"},Fe={key:0,class:"no-favorites"};function be(e,t,p,v,o,i){const V=d("Filter"),F=d("el-icon"),f=d("el-button"),T=d("Star"),I=d("Download"),P=d("el-tag"),c=d("el-table-column"),b=d("el-table"),O=d("el-card"),S=d("el-option"),Y=d("el-select"),k=d("el-form-item"),N=d("el-date-picker"),E=d("el-input"),R=d("Delete"),B=d("Plus"),J=d("el-form"),M=d("el-dialog"),L=K("loading");return r(),m("div",le,[_("div",se,[l(O,{class:"report-card"},{header:s(()=>[_("div",oe,[re,_("div",ie,[_("div",ne,[l(f,{type:"primary",onClick:i.showFilterDialog},{default:s(()=>[l(F,null,{default:s(()=>[l(V)]),_:1}),u(" 筛选条件 ")]),_:1},8,["onClick"]),l(f,{type:"warning",onClick:i.showFavoritesDialog},{default:s(()=>[l(F,null,{default:s(()=>[l(T)]),_:1}),u(" 收藏列表 ")]),_:1},8,["onClick"])]),_("div",ce,[l(f,{type:"primary",onClick:i.exportData},{default:s(()=>[l(F,null,{default:s(()=>[l(I)]),_:1}),u(" 导出Excel ")]),_:1},8,["onClick"])])])])]),default:s(()=>[i.hasActiveFilters?(r(),m("div",de,[he,(r(!0),m(y,null,C(o.selectedDataCenters,a=>(r(),g(P,{key:a,closable:"",onClose:h=>i.removeDataCenter(a),class:"filter-tag",type:"success"},{default:s(()=>[u(" 机房: "+w(a),1)]),_:2},1032,["onClose"]))),128)),(r(!0),m(y,null,C(o.selectedOperationStatuses,a=>(r(),g(P,{key:a,closable:"",onClose:h=>i.removeOperationStatus(a),class:"filter-tag",type:"warning"},{default:s(()=>[u(" 运行状态: "+w(a),1)]),_:2},1032,["onClose"]))),128)),o.selectedInnovativeTechFlag?(r(),g(P,{key:0,closable:"",onClose:i.removeInnovativeTechFlag,class:"filter-tag",type:"danger"},{default:s(()=>[u(" 是否信创: "+w(o.selectedInnovativeTechFlag),1)]),_:1},8,["onClose"])):x("",!0),(r(!0),m(y,null,C(o.timePeriods,(a,h)=>(r(),g(P,{key:h,closable:"",onClose:D=>i.removeTimePeriod(h),class:"filter-tag",type:"info"},{default:s(()=>[u(" 时间段: "+w(a.label||i.formatDate(a.startDate)+" 至 "+i.formatDate(a.endDate)),1)]),_:2},1032,["onClose"]))),128)),l(f,{link:"",onClick:i.clearAllFilters,class:"clear-filters"},{default:s(()=>[u(" 清除所有筛选 ")]),_:1},8,["onClick"])])):x("",!0),H((r(),m("div",ue,[l(b,{data:o.deviceAgeData,border:"",stripe:"","highlight-current-row":"","header-cell-style":{backgroundColor:"#f5f7fa",color:"#606266",fontWeight:"bold"},ref:"deviceAgeTable"},{default:s(()=>[l(c,{prop:"data_center",label:"机房",width:"180",align:"left"}),l(c,{label:"全部设备",width:"100",align:"right"},{default:s(a=>[u(w(a.row.total_count),1)]),_:1}),(r(!0),m(y,null,C(o.timePeriods,(a,h)=>(r(),g(c,{key:h,label:a.label||i.formatDate(a.startDate)+" 至 "+i.formatDate(a.endDate)},{default:s(()=>[l(c,{prop:"period_"+h+"_count",label:"数量",width:"80",align:"right"},null,8,["prop"]),l(c,{label:"占比",width:"100",align:"right"},{default:s(D=>[_("span",{class:$(i.getPercentageClass(D.row["period_"+h+"_percentage"],h===o.timePeriods.length-1))},w(D.row["period_"+h+"_percentage"])+"% ",3)]),_:2},1024)]),_:2},1032,["label"]))),128))]),_:1},8,["data"])])),[[L,o.loading]])]),_:1})]),l(M,{modelValue:o.filterDialogVisible,"onUpdate:modelValue":t[5]||(t[5]=a=>o.filterDialogVisible=a),title:"设置筛选条件",width:"650px","destroy-on-close":"","close-on-click-modal":!1,"custom-class":"custom-dialog"},{footer:s(()=>[_("span",De,[l(f,{onClick:t[4]||(t[4]=a=>o.filterDialogVisible=!1)},{default:s(()=>[u("取消")]),_:1}),l(f,{type:"primary",onClick:i.applyFilters},{default:s(()=>[u("应用筛选")]),_:1},8,["onClick"])])]),default:s(()=>[l(J,{model:o.filterForm,"label-width":"100px"},{default:s(()=>[l(k,{label:"机房"},{default:s(()=>[l(Y,{modelValue:o.filterForm.dataCenters,"onUpdate:modelValue":t[0]||(t[0]=a=>o.filterForm.dataCenters=a),multiple:"",filterable:"",placeholder:"请选择机房",style:{width:"100%"}},{default:s(()=>[(r(!0),m(y,null,C(o.dataCenterOptions,a=>(r(),g(S,{key:a.data_center,label:a.data_center,value:a.data_center},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(k,{label:"运行状态"},{default:s(()=>[l(Y,{modelValue:o.filterForm.operationStatuses,"onUpdate:modelValue":t[1]||(t[1]=a=>o.filterForm.operationStatuses=a),multiple:"",filterable:"",placeholder:"请选择运行状态",style:{width:"100%"}},{default:s(()=>[(r(!0),m(y,null,C(o.operationStatusOptions,a=>(r(),g(S,{key:a.operation_status,label:a.operation_status,value:a.operation_status},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(k,{label:"是否信创"},{default:s(()=>[l(Y,{modelValue:o.filterForm.innovativeTechFlag,"onUpdate:modelValue":t[2]||(t[2]=a=>o.filterForm.innovativeTechFlag=a),filterable:"",placeholder:"请选择是否信创",clearable:"",style:{width:"100%"}},{default:s(()=>[(r(!0),m(y,null,C(o.innovativeTechOptions,a=>(r(),g(S,{key:a.is_innovative_tech,label:a.is_innovative_tech,value:a.is_innovative_tech},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(k,{label:"时间段"},{default:s(()=>[(r(!0),m(y,null,C(o.filterForm.timePeriods,(a,h)=>(r(),m("div",{key:h,class:"time-period-row"},[l(N,{modelValue:a.startDate,"onUpdate:modelValue":D=>a.startDate=D,type:"date",placeholder:"开始日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"140px"}},null,8,["modelValue","onUpdate:modelValue"]),me,l(N,{modelValue:a.endDate,"onUpdate:modelValue":D=>a.endDate=D,type:"date",placeholder:"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"140px"}},null,8,["modelValue","onUpdate:modelValue"]),fe,l(E,{modelValue:a.label,"onUpdate:modelValue":D=>a.label=D,placeholder:"自定义标签",style:{width:"120px"}},null,8,["modelValue","onUpdate:modelValue"]),l(f,{type:"danger",circle:"",onClick:D=>i.removeFilterTimePeriod(h),disabled:o.filterForm.timePeriods.length<=1,style:{"margin-left":"10px"}},{default:s(()=>[l(F,null,{default:s(()=>[l(R)]),_:1})]),_:2},1032,["onClick","disabled"])]))),128)),_("div",_e,[l(f,{type:"primary",onClick:i.addTimePeriod,disabled:o.filterForm.timePeriods.length>=5},{default:s(()=>[l(F,null,{default:s(()=>[l(B)]),_:1}),u(" 添加时间段 ")]),_:1},8,["onClick","disabled"]),o.filterForm.timePeriods.length>=5?(r(),m("span",pe," (最多支持5个时间段) ")):x("",!0)])]),_:1}),l(k,{label:"收藏设置"},{default:s(()=>[_("div",ge,[l(E,{modelValue:o.favoriteForm.name,"onUpdate:modelValue":t[3]||(t[3]=a=>o.favoriteForm.name=a),placeholder:"输入收藏名称",style:{width:"200px"}},null,8,["modelValue"]),l(f,{type:"success",onClick:i.saveFilterFavorite,disabled:!o.favoriteForm.name,style:{"margin-left":"10px"}},{default:s(()=>[l(F,null,{default:s(()=>[l(T)]),_:1}),u(" 保存收藏 ")]),_:1},8,["onClick","disabled"])])]),_:1}),o.filterFavorites.length>0?(r(),g(k,{key:0,label:"收藏列表"},{default:s(()=>[_("div",ve,[(r(!0),m(y,null,C(o.filterFavorites,a=>(r(),g(P,{key:a.id,class:"favorite-tag",closable:"",onClose:h=>i.deleteFilterFavorite(a.id),onClick:h=>i.applyFilterFavorite(a)},{default:s(()=>[u(w(a.name),1)]),_:2},1032,["onClose","onClick"]))),128))])]),_:1})):x("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(M,{modelValue:o.favoritesDialogVisible,"onUpdate:modelValue":t[6]||(t[6]=a=>o.favoritesDialogVisible=a),title:"收藏的筛选条件",width:"500px","destroy-on-close":"","close-on-click-modal":!1,"custom-class":"custom-dialog"},{default:s(()=>[o.filterFavorites.length===0?(r(),m("div",Fe," 暂无收藏的筛选条件 ")):(r(),g(b,{key:1,data:o.filterFavorites,style:{width:"100%"}},{default:s(()=>[l(c,{prop:"name",label:"名称"}),l(c,{prop:"created_at",label:"创建时间",width:"180"},{default:s(a=>[u(w(new Date(a.row.created_at).toLocaleString()),1)]),_:1}),l(c,{label:"操作",width:"150"},{default:s(a=>[l(f,{type:"primary",size:"small",onClick:h=>i.applyFilterFavorite(a.row)},{default:s(()=>[u("应用")]),_:2},1032,["onClick"]),l(f,{type:"danger",size:"small",onClick:h=>i.deleteFilterFavorite(a.row.id)},{default:s(()=>[u("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]))]),_:1},8,["modelValue"])])}const we=z(ae,[["render",be],["__scopeId","data-v-cf99c4f3"]]);export{we as default};
