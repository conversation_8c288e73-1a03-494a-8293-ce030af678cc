# 生产环境405错误和静态资源404错误修复指南

## 问题描述

生产环境中出现两类主要错误：

### 1. API 405错误
用户在全局搜索后点击结果跳转到设备管理页面时，出现：
- `Failed to load resource: the server responded with a status of 405 (Not Allowed)`
- 数据字典API调用失败，返回405状态码

### 2. 静态资源404错误
访问某些页面时，静态资源加载失败：
- `GET http://172.19.129.81/cmdb_device_management/assets/index-_fWb8xVf.js net::ERR_ABORTED 404 (Not Found)`
- `GET http://172.19.129.81/cmdb_device_management/assets/index-BZoqYgHj.css net::ERR_ABORTED 404 (Not Found)`

## 问题分析

### API 405错误原因
1. **Nginx代理配置冲突**：生产环境有两个不同的server块，可能导致请求被错误处理
2. **负载均衡问题**：`cmdb-web` upstream配置可能导致HTTP方法被改变
3. **缓存机制**：某些缓存配置可能缓存了错误的响应

### 静态资源404错误原因
1. **Vue Router History模式问题**：当访问 `/cmdb_device_management` 时，浏览器认为这是一个目录
2. **相对路径解析错误**：静态资源的相对路径变成了 `/cmdb_device_management/assets/`
3. **Nginx配置缺失**：缺少专门的 `/assets/` 路径处理规则
4. **SPA路由冲突**：单页应用路由与静态资源路径冲突

## 解决方案

### 方案一：立即修复（推荐）

1. **部署后端修复代码**
   ```bash
   # 重启后端服务
   cd /opt/cmdb_v2.0/backend
   pm2 restart cmdb-backend
   ```

2. **部署前端调试工具**
   ```bash
   # 重新构建前端
   cd /opt/cmdb_v2.0/frontend
   npm run build
   ```

3. **使用调试页面测试**
   - API调试：`http://*************:9000/debug_api_test`
   - 静态资源测试：`http://*************:9000/static_resource_test`
   - 点击各个测试按钮，查看详细的错误信息
   - 检查后端日志：`pm2 logs cmdb-backend`

### 方案二：Nginx配置修复（根本解决）

#### 主服务器（*************）修复

1. **备份当前配置**
   ```bash
   cp /etc/nginx/conf.d/cmdb_129.81.conf /etc/nginx/conf.d/cmdb_129.81.conf.backup
   ```

2. **应用修复配置**
   ```bash
   cp /opt/cmdb_v2.0/frontend/nginx_config/cmdb_129.81_fixed.conf /etc/nginx/conf.d/cmdb_129.81.conf
   ```

3. **测试配置**
   ```bash
   nginx -t
   ```

4. **重载Nginx**
   ```bash
   nginx -s reload
   ```

#### 备用服务器（*************）修复

1. **备份当前配置**
   ```bash
   cp /etc/nginx/conf.d/cmdb_129.82.conf /etc/nginx/conf.d/cmdb_129.82.conf.backup
   ```

2. **应用修复配置**
   ```bash
   cp /home/<USER>/cmdb_v2.0/frontend/nginx_config/cmdb_129.82_fixed.conf /etc/nginx/conf.d/cmdb_129.82.conf
   ```

3. **测试配置**
   ```bash
   nginx -t
   ```

4. **重载Nginx**
   ```bash
   nginx -s reload
   ```

#### 验证两台服务器修复效果

1. **检查主服务器**
   ```bash
   curl -I http://*************:9000/health
   curl -I http://*************/health
   ```

2. **检查备用服务器**
   ```bash
   curl -I http://*************:9000/health
   ```

3. **检查调试头信息**
   ```bash
   curl -I http://*************:9000/api/test_method
   curl -I http://*************:9000/api/test_method
   ```

### 方案三：临时绕过（紧急情况）

如果上述方案都无法立即实施，可以使用以下临时解决方案：

1. **修改前端请求方式**
   在 `cmdb_device_management.vue` 中临时添加重试机制：

   ```javascript
   async getDatadict(dictCode, targetArray) {
     try {
       const response = await this.$axios.post('/api/get_cmdb_data_dictionary', {
         dict_code: dictCode,
       });
       this[targetArray] = response.data.msg;
     } catch (error) {
       if (error.response?.status === 405) {
         // 405错误时，尝试使用GET方法（临时方案）
         try {
           const getResponse = await this.$axios.get(`/api/get_cmdb_data_dictionary?dict_code=${dictCode}`);
           this[targetArray] = getResponse.data.msg;
           return;
         } catch (getError) {
           console.error("GET方法也失败:", getError);
         }
       }
       console.error("数据加载失败:", error);
       this.$message.error("数据加载失败");
     }
   }
   ```

## 调试步骤

### 1. 检查后端日志
```bash
pm2 logs cmdb-backend --lines 50
```

查找包含 "=== 数据字典API调用 ===" 的日志条目，检查：
- 请求方法是否为POST
- 请求头信息
- Nginx代理信息

### 2. 使用调试页面
访问 `/debug_api_test` 页面，依次点击：
1. "测试数据字典API (POST)" - 应该成功
2. "测试数据字典API (GET)" - 应该返回405错误
3. "测试方法检测API" - 查看nginx代理信息
4. "测试不同请求头" - 测试缓存绕过

### 3. 检查网络请求
在浏览器开发者工具中：
1. 打开Network标签
2. 重现问题
3. 查看失败的请求详情：
   - 请求方法
   - 请求头
   - 响应头
   - 响应内容

## 预防措施

1. **统一环境配置**
   - 确保开发、测试、生产环境的nginx配置一致
   - 定期同步配置文件

2. **监控和告警**
   - 添加API错误率监控
   - 设置405错误告警

3. **测试覆盖**
   - 在部署前进行完整的功能测试
   - 包含不同环境的兼容性测试

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 后端日志（包含调试信息）
2. 浏览器Network标签的请求详情截图
3. 调试页面的测试结果
4. 当前使用的nginx配置文件

## 更新记录

- 2025-05-29：创建初始版本
- 2025-05-29：添加临时解决方案和详细调试步骤
