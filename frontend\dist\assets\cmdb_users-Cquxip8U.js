import{_ as T,z as j,v as q,A as L,c as y,a as l,w as r,f as t,b as d,F as S,l as P,h,x as w,B as M,C as G,m as C,E as D,p as H,q as J,e as f}from"./index-BnaD8Tdo.js";import{u as U,w as K,F as Q}from"./FileSaver.min-CkyCPK_c.js";const W={components:{Plus:L,Search:q,Download:j},data(){var o,e,n;return{userArr:[],loading:!1,showRoleColumn:!1,hasDeletePermission:(o=localStorage.getItem("role_code"))==null?void 0:o.includes("D"),hasUpdatePermission:(e=localStorage.getItem("role_code"))==null?void 0:e.includes("U"),hasInsertPermission:(n=localStorage.getItem("role_code"))==null?void 0:n.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{username:"",total:0,pageSize:10,currentPage:1,loginUsername:localStorage.getItem("loginUsername"),sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,username:"",password:"",role_code:"",created_at:null,role_code_name:"",real_name:"",phone:"",email:"",wechat_id:"",calendar_duty_permission:""},selectedRoles:[],roleOptions:[{value:"I",label:"I:增"},{value:"D",label:"D:删"},{value:"U",label:"U:改"}]}},mounted(){this.loadData()},methods:{handlePageChange(o){this.search.currentPage=o,this.loadData()},handlePageSizeChange(o){this.search.pageSize=parseInt(o),this.search.currentPage=1,this.loadData()},updateRoleCode(o){this.formData.role_code=o.join(",")},handleSortChange({column:o,prop:e,order:n}){this.search.sortProp=e,this.search.sortOrder=n==="ascending"?"asc":"desc",this.loadData()},async loadData(){try{this.loading=!0;const o=await this.$axios.post("/api/get_cmdb_users",this.search);this.userArr=o.data.msg,this.search.total=o.data.total}catch(o){console.error("数据加载失败:",o),this.$message.error("数据加载失败")}finally{this.loading=!1}},async submitAdd(){if(this.formData.username=this.formData.username.trim(),!this.formData.username||!this.formData.password){alert("请输入用户名和密码！");return}this.formData.usernameby=this.search.loginUsername;try{await this.$axios.post("/api/add_cmdb_users",this.formData),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(o){console.error("添加失败:",o),o.response&&o.response.data&&o.response.data.msg?this.$message.error(`添加失败: ${o.response.data.msg}`):this.$message.error("添加失败")}},async loadCalendarDutyPermission(o){try{const e=await this.$axios.post("/api/check_ops_calendar_duty_permission",{username:o});if(e.data.code===0){const n=e.data.msg;console.log("获取到的权限信息:",n),n.hasEditPermission?this.formData.calendar_duty_permission="edit":n.hasViewPermission?this.formData.calendar_duty_permission="view":this.formData.calendar_duty_permission="",console.log("设置的权限值:",this.formData.calendar_duty_permission)}else this.formData.calendar_duty_permission=""}catch(e){console.error("获取交易日历权限失败:",e),this.formData.calendar_duty_permission=""}},async saveCalendarDutyPermission(o,e){if(this.search.loginUsername==="admin")try{const n=await this.$axios.post("/api/update_ops_calendar_duty_permission",{username:o,permission_type:e,updated_by:this.search.loginUsername});n.data.code!==0&&console.error("保存交易日历权限失败:",n.data.msg)}catch(n){console.error("保存交易日历权限失败:",n)}},async submitEdit(){this.formData.usernameby=this.search.loginUsername;const o={...this.formData};this.search.loginUsername!=="admin"&&(delete o.role_code,delete o.calendar_duty_permission);try{const e=await this.$axios.post("/api/update_cmdb_users",o);e.data.code===0?(this.search.loginUsername==="admin"&&await this.saveCalendarDutyPermission(this.formData.username,this.formData.calendar_duty_permission),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()):this.$message.error(`更新失败: ${e.data.msg}`)}catch(e){console.error("更新失败:",e),e.response&&e.response.data&&e.response.data.msg?this.$message.error(`更新失败: ${e.response.data.msg}`):this.$message.error("更新失败")}},async submitDelete(o){this.formData.usernameby=this.search.loginUsername;try{const e=await this.$axios.post("/api/del_cmdb_users",this.formData);e.data.code===0?D.success("删除成功"):D.error(`删除失败: ${e.data.msg}`),this.loadData(),this.dialogVisible.delete=!1}catch(e){console.error("删除失败:",e),e.response&&e.response.data&&e.response.data.msg?this.$message.error(`删除失败: ${e.response.data.msg}`):this.$message.error("删除失败")}},handleAdd(o,e){if(this.search.loginUsername!=="admin"){D.error("只有管理员可以添加用户");return}this.dialogVisible.add=!this.dialogVisible.add,this.formData={usernameby:this.search.loginUsername},this.selectedRoles=[]},async handleEdit(o,e){if(this.search.loginUsername!=="admin"&&e.username!==this.search.loginUsername){D.error("您只能编辑自己的信息");return}this.dialogVisible.edit=!0,this.formData.id=e.id,this.formData.username=e.username,this.formData.real_name=e.real_name||"",this.formData.phone=e.phone||"",this.formData.email=e.email||"",this.formData.wechat_id=e.wechat_id||"",this.formData.password="",this.formData.role_code=e.role_code,this.selectedRoles=e.role_code?e.role_code.replace(/^,|,$/g,"").split(","):[],this.search.loginUsername==="admin"&&await this.loadCalendarDutyPermission(e.username),this.formData.usernameby=this.search.loginUsername},handleDelete(o,e){if(this.search.loginUsername!=="admin"){D.error("只有管理员可以删除用户");return}this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=e.id,this.formData.username=e.username,this.formData.usernameby=this.search.loginUsername},exportData(){const e=this.$refs.table.columns,n=e.map(u=>u.label),v=this.userArr.map(u=>e.map(g=>u[g.property])),a=[n,...v],m=U.aoa_to_sheet(a),i=U.book_new();U.book_append_sheet(i,m,"Sheet1");const _=K(i,{bookType:"xlsx",type:"array"}),b=new Blob([_],{type:"application/octet-stream"});Q.saveAs(b,"用户管理.xlsx")}}},p=o=>(H("data-v-8b4872db"),o=o(),J(),o),X={class:"user-manage"},Y={class:"dialogdiv"},Z=p(()=>t("span",{class:"label"},"用户名:",-1)),$=p(()=>t("span",{class:"label"},"用户姓名:",-1)),ee=p(()=>t("span",{class:"label"},"联系电话:",-1)),ae=p(()=>t("span",{class:"label"},"电子邮箱:",-1)),le=p(()=>t("span",{class:"label"},"企业微信ID:",-1)),se=p(()=>t("span",{class:"label"},"初始密码:",-1)),oe=p(()=>t("span",{class:"label"},"权限代码:",-1)),te={class:"dialog-footer"},re={class:"dialogdiv"},ne=p(()=>t("span",{class:"label"},"用户名:",-1)),ie=p(()=>t("span",{class:"label"},"用户姓名:",-1)),de=p(()=>t("span",{class:"label"},"联系电话:",-1)),me=p(()=>t("span",{class:"label"},"电子邮箱:",-1)),ue=p(()=>t("span",{class:"label"},"企业微信ID:",-1)),pe={key:0},ce=p(()=>t("span",{class:"label"},"交易日历权限:",-1)),he=p(()=>t("span",{class:"label"},"密码:",-1)),_e=p(()=>t("span",{class:"label"},"权限代码:",-1)),fe={class:"dialog-footer"},be={style:{display:"flex","white-space":"nowrap"}},ge={class:"pagination"};function De(o,e,n,v,a,m){const i=d("el-input"),_=d("el-option"),b=d("el-select"),u=d("el-button"),g=d("el-dialog"),I=d("el-alert"),x=d("el-form-item"),z=d("Search"),V=d("el-icon"),R=d("Plus"),A=d("Download"),B=d("el-form"),k=d("el-card"),c=d("el-table-column"),E=d("el-table"),O=d("el-pagination"),F=G("loading");return f(),y("div",X,[l(g,{modelValue:a.dialogVisible.add,"onUpdate:modelValue":e[8]||(e[8]=s=>a.dialogVisible.add=s),title:"新增用户",width:"400","align-center":""},{footer:r(()=>[t("div",te,[l(u,{onClick:e[7]||(e[7]=s=>a.dialogVisible.add=!1)},{default:r(()=>[h("返回")]),_:1}),l(u,{type:"primary",onClick:m.submitAdd},{default:r(()=>[h("确定")]),_:1},8,["onClick"])])]),default:r(()=>[t("div",Y,[t("p",null,[Z,l(i,{modelValue:a.formData.username,"onUpdate:modelValue":e[0]||(e[0]=s=>a.formData.username=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),t("p",null,[$,l(i,{modelValue:a.formData.real_name,"onUpdate:modelValue":e[1]||(e[1]=s=>a.formData.real_name=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),t("p",null,[ee,l(i,{modelValue:a.formData.phone,"onUpdate:modelValue":e[2]||(e[2]=s=>a.formData.phone=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),t("p",null,[ae,l(i,{modelValue:a.formData.email,"onUpdate:modelValue":e[3]||(e[3]=s=>a.formData.email=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),t("p",null,[le,l(i,{modelValue:a.formData.wechat_id,"onUpdate:modelValue":e[4]||(e[4]=s=>a.formData.wechat_id=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),t("p",null,[se,l(i,{modelValue:a.formData.password,"onUpdate:modelValue":e[5]||(e[5]=s=>a.formData.password=s),style:{width:"240px"},clearable:"",type:"password"},null,8,["modelValue"])]),t("p",null,[oe,l(b,{modelValue:a.selectedRoles,"onUpdate:modelValue":e[6]||(e[6]=s=>a.selectedRoles=s),multiple:"",style:{width:"240px"},placeholder:"请选择角色",onChange:m.updateRoleCode},{default:r(()=>[(f(!0),y(S,null,P(a.roleOptions,s=>(f(),C(_,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])])])]),_:1},8,["modelValue"]),l(g,{modelValue:a.dialogVisible.edit,"onUpdate:modelValue":e[18]||(e[18]=s=>a.dialogVisible.edit=s),title:"更新用户信息",width:"400","align-center":""},{footer:r(()=>[t("div",fe,[l(u,{onClick:e[17]||(e[17]=s=>a.dialogVisible.edit=!1)},{default:r(()=>[h("取消")]),_:1}),l(u,{type:"primary",onClick:m.submitEdit},{default:r(()=>[h("更新")]),_:1},8,["onClick"])])]),default:r(()=>[t("div",re,[t("p",null,[ne,l(i,{modelValue:a.formData.username,"onUpdate:modelValue":e[9]||(e[9]=s=>a.formData.username=s),style:{width:"240px"},clearable:"",disabled:""},null,8,["modelValue"])]),t("p",null,[ie,l(i,{modelValue:a.formData.real_name,"onUpdate:modelValue":e[10]||(e[10]=s=>a.formData.real_name=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),t("p",null,[de,l(i,{modelValue:a.formData.phone,"onUpdate:modelValue":e[11]||(e[11]=s=>a.formData.phone=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),t("p",null,[me,l(i,{modelValue:a.formData.email,"onUpdate:modelValue":e[12]||(e[12]=s=>a.formData.email=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),t("p",null,[ue,l(i,{modelValue:a.formData.wechat_id,"onUpdate:modelValue":e[13]||(e[13]=s=>a.formData.wechat_id=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),a.search.loginUsername==="admin"?(f(),y("p",pe,[ce,l(b,{modelValue:a.formData.calendar_duty_permission,"onUpdate:modelValue":e[14]||(e[14]=s=>a.formData.calendar_duty_permission=s),style:{width:"240px"},placeholder:"请选择权限",clearable:""},{default:r(()=>[l(_,{label:"无权限",value:""}),l(_,{label:"查看权限（只能查看，无法编辑）",value:"view"}),l(_,{label:"编辑权限（可查看，可编辑）",value:"edit"})]),_:1},8,["modelValue"])])):w("",!0),t("p",null,[he,l(i,{modelValue:a.formData.password,"onUpdate:modelValue":e[15]||(e[15]=s=>a.formData.password=s),style:{width:"240px"},clearable:"",placeholder:"为空不会更新密码",type:"password"},null,8,["modelValue"])]),t("p",null,[_e,l(b,{modelValue:a.selectedRoles,"onUpdate:modelValue":e[16]||(e[16]=s=>a.selectedRoles=s),multiple:"",style:{width:"240px"},placeholder:"请选择角色",disabled:a.search.loginUsername!=="admin",onChange:m.updateRoleCode},{default:r(()=>[(f(!0),y(S,null,P(a.roleOptions,s=>(f(),C(_,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled","onChange"])])])]),_:1},8,["modelValue"]),l(g,{modelValue:a.dialogVisible.delete,"onUpdate:modelValue":e[20]||(e[20]=s=>a.dialogVisible.delete=s),title:"删除用户",width:"500","align-center":""},{footer:r(()=>[t("div",null,[l(u,{onClick:e[19]||(e[19]=s=>a.dialogVisible.delete=!1)},{default:r(()=>[h("取消")]),_:1}),l(u,{type:"danger",onClick:m.submitDelete},{default:r(()=>[h("确认删除")]),_:1},8,["onClick"])])]),default:r(()=>[l(I,{type:"warning",title:`确定要删除 IP 为 ${a.formData.username} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),l(k,{class:"search-card"},{default:r(()=>[l(B,{inline:!0},{default:r(()=>[l(x,{label:"用户名："},{default:r(()=>[l(i,{modelValue:a.search.username,"onUpdate:modelValue":e[21]||(e[21]=s=>a.search.username=s),placeholder:"请输入用户名",clearable:""},null,8,["modelValue"])]),_:1}),l(x,null,{default:r(()=>[l(u,{type:"primary",onClick:m.loadData},{default:r(()=>[l(V,null,{default:r(()=>[l(z)]),_:1}),h("查询 ")]),_:1},8,["onClick"]),l(u,{disabled:a.search.loginUsername!=="admin",type:"success",onClick:m.handleAdd},{default:r(()=>[l(V,null,{default:r(()=>[l(R)]),_:1}),h("新增 ")]),_:1},8,["disabled","onClick"]),l(u,{type:"info",onClick:m.exportData},{default:r(()=>[l(V,null,{default:r(()=>[l(A)]),_:1}),h("导出 ")]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1}),l(k,{class:"table-card"},{default:r(()=>[M((f(),C(E,{data:a.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:m.handleSortChange},{default:r(()=>[w("",!0),l(c,{prop:"username",label:"用户名",sortable:""}),l(c,{prop:"real_name",label:"用户姓名",sortable:""}),l(c,{prop:"phone",label:"联系电话",sortable:""}),l(c,{prop:"email",label:"电子邮箱",sortable:""}),l(c,{prop:"wechat_id",label:"企业微信ID",sortable:""}),l(c,{prop:"role_code_name",label:"权限",sortable:""}),w("",!0),l(c,{prop:"created_at",label:"创建时间",sortable:""}),l(c,{prop:"created_by",label:"创建人",sortable:""}),l(c,{prop:"updated_at",label:"更新时间",sortable:""}),l(c,{prop:"updated_by",label:"更新人",sortable:""}),l(c,{label:"操作",fixed:"right"},{default:r(s=>[t("div",be,[l(u,{size:"small",type:"warning",disabled:a.search.loginUsername!=="admin"&&s.row.username!==a.search.loginUsername,onClick:N=>m.handleEdit(s.$index,s.row)},{default:r(()=>[h("编辑")]),_:2},1032,["disabled","onClick"]),l(u,{size:"small",type:"danger",disabled:a.search.loginUsername!=="admin",onClick:N=>m.handleDelete(s.$index,s.row)},{default:r(()=>[h("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[F,a.loading]]),t("div",ge,[l(O,{background:"","current-page":a.search.currentPage,"page-size":a.search.pageSize,total:a.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:m.handlePageSizeChange,onCurrentChange:m.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const we=T(W,[["render",De],["__scopeId","data-v-8b4872db"]]);export{we as default};
