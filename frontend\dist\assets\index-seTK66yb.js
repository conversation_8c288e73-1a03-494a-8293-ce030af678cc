import{_ as Q,z as W,v as X,A as $,c as S,a as e,f as x,w as a,b as i,y as ee,F as z,l as L,h as m,B as ae,C as te,m as h,t as O,r as v,J as I,o as le,R as oe,E as b,O as ne,e as u}from"./index-BnaD8Tdo.js";import{s as F}from"./request-BdTIjj0p.js";const re={name:"OpsChangeManagement",components:{Plus:$,Search:X,Download:W},setup(){const w=oe(),s=v(null),n=I({keyword:"",changeLevel:"",system:"",requester:"",implementer:"",startDate:"",endDate:"",oaProcess:"未上传",signedArchive:"未上传",sortProp:"change_id",sortOrder:"desc"}),o=v([]),E=l=>{l?(n.startDate=l[0],n.endDate=l[1]):(n.startDate="",n.endDate="")},R=v([]),k=v(!1),c=I({currentPage:1,pageSize:10,total:0}),_=v([]),g=v([]),f=v([]),V=async()=>{try{const l=await F({url:"/api/get_cmdb_data_dictionary",method:"post",data:{dict_type:"P"}});l.code===0&&(_.value=l.msg)}catch(l){console.error("获取变更级别列表失败:",l),b.error("获取变更级别列表失败")}},U=async()=>{try{const l=await F({url:"/api/get_user_list",method:"post"});l.code===0&&(f.value=l.msg)}catch(l){console.error("获取用户列表失败:",l),b.error("获取用户列表失败")}},A=async()=>{try{const l=await F({url:"/api/get_system_list",method:"post"});l.code===0&&(g.value=l.msg)}catch(l){console.error("获取系统列表失败:",l),b.error("获取系统列表失败")}},p=async()=>{k.value=!0,console.log("搜索表单数据:",n);try{const l={...n,currentPage:c.currentPage,pageSize:c.pageSize,change_level:n.changeLevel};console.log("发送请求数据:",l);const r=await F({url:"/api/get_ops_change_management",method:"post",data:l});r.code===0?(R.value=r.msg,c.total=r.total):b.error(`获取列表失败: ${r.msg}`)}catch(l){console.error("获取变更管理列表失败:",l),b.error("获取变更管理列表失败")}finally{k.value=!1}},C=({prop:l,order:r})=>{n.sortProp=l,n.sortOrder=r==="ascending"?"asc":"desc",p()},M=()=>{c.currentPage=1,p()},P=()=>{n.keyword="",n.changeLevel="",n.system="",n.requester="",n.implementer="",n.oaProcess="未上传",n.signedArchive="未上传",n.startDate="",n.endDate="",n.sortProp="change_id",n.sortOrder="desc",o.value=[],c.currentPage=1,s.value&&s.value.resetFields(),p(),b.success("搜索条件已重置")},q=l=>{c.pageSize=l,p()},d=l=>{c.currentPage=l,p()},y=()=>{w.push("/ops_change_management/detail/new")},B=l=>{w.push(`/ops_change_management/detail/${l.id}`)},N=l=>{w.push(`/ops_change_management/detail/${l.id}?tab=attachments`)},Y=l=>{ne.confirm(`确定要删除变更 "${l.change_id}" 吗？此操作不可恢复。`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const r=await F({url:"/api/del_ops_change_management",method:"post",data:{id:l.id,usernameby:localStorage.getItem("loginUsername")||"admin"}});r.code===0?(b.success("删除成功"),p()):b.error(`删除失败: ${r.msg}`)}catch(r){console.error("删除变更失败:",r),b.error("删除变更失败")}}).catch(()=>{})},t=l=>{if(!l)return"";try{const r=new Date(l);if(isNaN(r.getTime()))return l;const D=G=>String(G).padStart(2,"0"),T=r.getFullYear(),K=D(r.getMonth()+1),H=D(r.getDate()),j=D(r.getHours()),J=D(r.getMinutes()),Z=D(r.getSeconds());return r.getHours()===0&&r.getMinutes()===0&&r.getSeconds()===0?`${T}-${K}-${H}`:`${T}-${K}-${H} ${j}:${J}:${Z}`}catch(r){return console.error("日期格式化错误:",r),l}};return le(async()=>{w.currentRoute.value.query.search_ip&&(n.keyword=w.currentRoute.value.query.search_ip,console.log("从全局搜索跳转，设置关键词:",n.keyword)),await Promise.all([V(),U(),A()]),p()}),{searchFormRef:s,searchForm:n,dateRange:o,tableData:R,tableLoading:k,pagination:c,changeLevelOptions:_,systemOptions:g,userOptions:f,formatDateTime:t,handleDateRangeChange:E,handleSearch:M,resetSearch:P,handleSizeChange:q,handleCurrentChange:d,handleSortChange:C,handleAdd:y,handleView:B,handleUpload:N,handleDelete:Y}}},se={class:"app-container"},ce={class:"button-container"},de={class:"action-bar unified-action-bar"},ie={class:"action-bar-left"},ue={style:{display:"flex","white-space":"nowrap"}},me={class:"pagination"};function _e(w,s,n,o,E,R){const k=i("el-input"),c=i("el-form-item"),_=i("el-col"),g=i("el-option"),f=i("el-select"),V=i("el-row"),U=i("el-date-picker"),A=i("Search"),p=i("el-icon"),C=i("el-button"),M=i("el-form"),P=i("el-card"),q=i("Plus"),d=i("el-table-column"),y=i("el-tag"),B=i("el-table"),N=i("el-pagination"),Y=te("loading");return u(),S("div",se,[e(P,{class:"search-card"},{default:a(()=>[e(M,{model:o.searchForm,ref:"searchFormRef","label-width":"100px","label-position":"right"},{default:a(()=>[e(V,{gutter:20},{default:a(()=>[e(_,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(c,{label:"关键字"},{default:a(()=>[e(k,{modelValue:o.searchForm.keyword,"onUpdate:modelValue":s[0]||(s[0]=t=>o.searchForm.keyword=t),placeholder:"变更编号/变更名称",clearable:"",onKeyup:ee(o.handleSearch,["enter"]),class:"form-control"},null,8,["modelValue","onKeyup"])]),_:1})]),_:1}),e(_,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(c,{label:"变更级别"},{default:a(()=>[e(f,{modelValue:o.searchForm.changeLevel,"onUpdate:modelValue":s[1]||(s[1]=t=>o.searchForm.changeLevel=t),placeholder:"请选择",clearable:"",class:"form-control"},{default:a(()=>[(u(!0),S(z,null,L(o.changeLevelOptions,t=>(u(),h(g,{key:t.dict_code,label:t.dict_name,value:t.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(c,{label:"变更系统"},{default:a(()=>[e(f,{modelValue:o.searchForm.system,"onUpdate:modelValue":s[2]||(s[2]=t=>o.searchForm.system=t),placeholder:"请选择",clearable:"",class:"form-control"},{default:a(()=>[(u(!0),S(z,null,L(o.systemOptions,t=>(u(),h(g,{key:t.system_abbreviation,label:t.system_abbreviation,value:t.system_abbreviation},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(c,{label:"变更负责人"},{default:a(()=>[e(f,{modelValue:o.searchForm.requester,"onUpdate:modelValue":s[3]||(s[3]=t=>o.searchForm.requester=t),placeholder:"请选择",clearable:"",class:"form-control"},{default:a(()=>[(u(!0),S(z,null,L(o.userOptions,t=>(u(),h(g,{key:t.username,label:t.real_name,value:t.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:20},{default:a(()=>[e(_,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(c,{label:"变更实施人"},{default:a(()=>[e(f,{modelValue:o.searchForm.implementer,"onUpdate:modelValue":s[4]||(s[4]=t=>o.searchForm.implementer=t),placeholder:"请选择",clearable:"",class:"form-control"},{default:a(()=>[(u(!0),S(z,null,L(o.userOptions,t=>(u(),h(g,{key:t.username,label:t.real_name,value:t.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(c,{label:"OA流程"},{default:a(()=>[e(f,{modelValue:o.searchForm.oaProcess,"onUpdate:modelValue":s[5]||(s[5]=t=>o.searchForm.oaProcess=t),placeholder:"请选择",clearable:"",class:"form-control"},{default:a(()=>[e(g,{label:"已上传",value:"已上传"}),e(g,{label:"未上传",value:"未上传"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(c,{label:"签字存档"},{default:a(()=>[e(f,{modelValue:o.searchForm.signedArchive,"onUpdate:modelValue":s[6]||(s[6]=t=>o.searchForm.signedArchive=t),placeholder:"请选择",clearable:"",class:"form-control"},{default:a(()=>[e(g,{label:"已上传",value:"已上传"}),e(g,{label:"未上传",value:"未上传"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(c,{label:"变更时间"},{default:a(()=>[e(U,{modelValue:o.dateRange,"onUpdate:modelValue":s[7]||(s[7]=t=>o.dateRange=t),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",onChange:o.handleDateRangeChange,class:"form-control"},null,8,["modelValue","onChange"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:20},{default:a(()=>[e(_,{xs:24,sm:12,md:8,lg:24,class:"search-buttons-col"},{default:a(()=>[e(c,null,{default:a(()=>[x("div",ce,[e(C,{type:"primary",onClick:o.handleSearch},{default:a(()=>[e(p,null,{default:a(()=>[e(A)]),_:1}),m("查询 ")]),_:1},8,["onClick"]),e(C,{onClick:o.resetSearch},{default:a(()=>[m("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),x("div",de,[x("div",ie,[e(C,{type:"success",onClick:o.handleAdd},{default:a(()=>[e(p,null,{default:a(()=>[e(q)]),_:1}),m("新增变更 ")]),_:1},8,["onClick"])])]),e(P,{class:"table-card"},{default:a(()=>[ae((u(),h(B,{data:o.tableData,border:"",stripe:"",style:{width:"100%"},"table-layout":"auto","header-cell-style":{background:"#f5f7fa",color:"#606266"},onSortChange:o.handleSortChange},{default:a(()=>[e(d,{prop:"change_id",label:"变更编号","min-width":"120",sortable:""}),e(d,{prop:"title",label:"变更名称","min-width":"150","show-overflow-tooltip":"",sortable:""}),e(d,{prop:"system",label:"变更系统","min-width":"100",sortable:""}),e(d,{prop:"change_level_name_display",label:"变更级别","min-width":"100",sortable:""}),e(d,{prop:"formatted_change_time",label:"计划变更时间","min-width":"150",sortable:""}),e(d,{label:"OA流程","min-width":"100",align:"center",sortable:""},{default:a(t=>[t.row.oa_process?(u(),h(y,{key:0,type:"success"},{default:a(()=>[m("已上传")]),_:1})):(u(),h(y,{key:1,type:"info"},{default:a(()=>[m("未上传")]),_:1}))]),_:1}),e(d,{label:"签字存档","min-width":"100",align:"center",sortable:""},{default:a(t=>[t.row.signed_archive?(u(),h(y,{key:0,type:"success"},{default:a(()=>[m("已上传")]),_:1})):(u(),h(y,{key:1,type:"info"},{default:a(()=>[m("未上传")]),_:1}))]),_:1}),e(d,{label:"变更操作表","min-width":"100",align:"center",sortable:""},{default:a(t=>[t.row.operation_sheet?(u(),h(y,{key:0,type:"success"},{default:a(()=>[m("已上传")]),_:1})):(u(),h(y,{key:1,type:"info"},{default:a(()=>[m("未上传")]),_:1}))]),_:1}),e(d,{prop:"requester_name",label:"变更负责人","min-width":"120",sortable:""}),e(d,{prop:"implementers_name",label:"变更实施人","min-width":"120",sortable:"","show-overflow-tooltip":""}),e(d,{label:"创建时间","min-width":"150",sortable:"created_at"},{default:a(t=>[m(O(o.formatDateTime(t.row.created_at)),1)]),_:1}),e(d,{label:"创建人","min-width":"100",sortable:"created_by"},{default:a(t=>[m(O(t.row.created_by),1)]),_:1}),e(d,{label:"更新时间","min-width":"150",sortable:"updated_at"},{default:a(t=>[m(O(o.formatDateTime(t.row.updated_at)),1)]),_:1}),e(d,{label:"更新人","min-width":"100",sortable:"updated_by"},{default:a(t=>[m(O(t.row.updated_by),1)]),_:1}),e(d,{label:"操作",fixed:"right","min-width":"200"},{default:a(t=>[x("div",ue,[e(C,{type:"primary",size:"small",onClick:l=>o.handleView(t.row)},{default:a(()=>[m(" 详情 ")]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[Y,o.tableLoading]]),x("div",me,[e(N,{background:"","current-page":o.pagination.currentPage,"page-size":o.pagination.pageSize,total:o.pagination.total,"page-sizes":[10,20,50,100,1e3],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:o.handleSizeChange,onCurrentChange:o.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const pe=Q(re,[["render",_e],["__scopeId","data-v-a985b0a9"]]);export{pe as default};
