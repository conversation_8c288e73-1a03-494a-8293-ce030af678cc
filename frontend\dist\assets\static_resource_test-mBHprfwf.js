import{_ as g,c as i,f as s,ab as y,t as o,F as r,l as u,a as m,x as S,w as v,h as f,b as x,p as k,q as b,e as n,n as _}from"./index-BnaD8Tdo.js";const A={name:"StaticResourceTest",data(){return{currentUrl:window.location.href,baseUrl:window.location.origin,cssFiles:[],jsFiles:[],testResults:[]}},mounted(){this.detectLoadedResources()},methods:{detectLoadedResources(){const a=document.querySelectorAll('link[rel="stylesheet"]');this.cssFiles=Array.from(a).map(e=>({href:e.href,loaded:e.sheet!==null}));const l=document.querySelectorAll("script[src]");this.jsFiles=Array.from(l).map(e=>({src:e.src,loaded:e.readyState==="complete"||e.readyState==="loaded"}))},async testStaticResource(){this.testResults=[];const a=["/assets/index.js","/assets/index.css","/assets/logo.png",`${this.baseUrl}/assets/index.js`,`${this.baseUrl}/assets/index.css`];for(const l of a)try{const e=await fetch(l,{method:"HEAD"});this.testResults.push({url:l,success:e.ok,status:e.status,message:e.ok?"资源可访问":`HTTP ${e.status}`})}catch(e){this.testResults.push({url:l,success:!1,status:"ERROR",message:e.message})}},async testApiAccess(){var a;try{const l=await this.$axios.post("/api/test_method",{test:"static_resource_page"});this.testResults.push({url:"/api/test_method",success:!0,status:200,message:"API访问正常"})}catch(l){this.testResults.push({url:"/api/test_method",success:!1,status:((a=l.response)==null?void 0:a.status)||"ERROR",message:l.message})}}}},c=a=>(k("data-v-a3548568"),a=a(),b(),a),w={class:"static-test-container"},C=c(()=>s("h2",null,"静态资源加载测试页面",-1)),U={class:"test-section"},F=c(()=>s("h3",null,"当前页面信息",-1)),I={class:"info-grid"},L={class:"info-item"},N=c(()=>s("label",null,"当前URL:",-1)),V={class:"info-item"},B=c(()=>s("label",null,"Base URL:",-1)),E={class:"info-item"},T=c(()=>s("label",null,"路由路径:",-1)),$={class:"info-item"},j=c(()=>s("label",null,"路由名称:",-1)),q={class:"test-section"},P=c(()=>s("h3",null,"静态资源路径测试",-1)),D={class:"resource-tests"},H={class:"test-item"},O=c(()=>s("h4",null,"CSS文件检测",-1)),z={class:"resource-list"},J={class:"resource-path"},G={class:"test-item"},K=c(()=>s("h4",null,"JS文件检测",-1)),M={class:"resource-list"},Q={class:"resource-path"},W={class:"test-section"},X=c(()=>s("h3",null,"网络请求测试",-1)),Y={key:0,class:"test-results"},Z=c(()=>s("h4",null,"测试结果",-1)),ss={class:"test-url"},ts={class:"test-message"},es=y(`<div class="test-section" data-v-a3548568><h3 data-v-a3548568>解决方案建议</h3><div class="solutions" data-v-a3548568><div class="solution-item" data-v-a3548568><h4 data-v-a3548568>如果看到404错误：</h4><ol data-v-a3548568><li data-v-a3548568>检查nginx配置是否包含 <code data-v-a3548568>location /assets/</code> 规则</li><li data-v-a3548568>确认静态文件目录路径正确</li><li data-v-a3548568>重载nginx配置：<code data-v-a3548568>nginx -s reload</code></li></ol></div><div class="solution-item" data-v-a3548568><h4 data-v-a3548568>nginx配置示例：</h4><pre data-v-a3548568><code data-v-a3548568>location /assets/ {
    root /path/to/frontend/dist;
    expires max;
    try_files $uri $uri/ =404;
}</code></pre></div></div></div>`,1);function as(a,l,e,os,d,h){const p=x("el-button");return n(),i("div",w,[C,s("div",U,[F,s("div",I,[s("div",L,[N,s("span",null,o(d.currentUrl),1)]),s("div",V,[B,s("span",null,o(d.baseUrl),1)]),s("div",E,[T,s("span",null,o(a.$route.path),1)]),s("div",$,[j,s("span",null,o(a.$route.name),1)])])]),s("div",q,[P,s("div",D,[s("div",H,[O,s("div",z,[(n(!0),i(r,null,u(d.cssFiles,t=>(n(),i("div",{key:t.href,class:"resource-item"},[s("span",J,o(t.href),1),s("span",{class:_(["status",t.loaded?"loaded":"failed"])},o(t.loaded?"✓ 已加载":"✗ 加载失败"),3)]))),128))])]),s("div",G,[K,s("div",M,[(n(!0),i(r,null,u(d.jsFiles,t=>(n(),i("div",{key:t.src,class:"resource-item"},[s("span",Q,o(t.src),1),s("span",{class:_(["status",t.loaded?"loaded":"failed"])},o(t.loaded?"✓ 已加载":"✗ 加载失败"),3)]))),128))])])])]),s("div",W,[X,m(p,{onClick:h.testStaticResource,type:"primary"},{default:v(()=>[f("测试静态资源访问")]),_:1},8,["onClick"]),m(p,{onClick:h.testApiAccess,type:"success"},{default:v(()=>[f("测试API访问")]),_:1},8,["onClick"]),d.testResults.length>0?(n(),i("div",Y,[Z,(n(!0),i(r,null,u(d.testResults,(t,R)=>(n(),i("div",{key:R,class:"test-result"},[s("span",ss,o(t.url),1),s("span",{class:_(["test-status",t.success?"success":"error"])},o(t.success?"✓":"✗")+" "+o(t.status),3),s("span",ts,o(t.message),1)]))),128))])):S("",!0)]),es])}const ls=g(A,[["render",as],["__scopeId","data-v-a3548568"]]);export{ls as default};
