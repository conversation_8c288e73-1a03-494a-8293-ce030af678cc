<template>
  <div class="event-management">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <h2>事件管理</h2>
          <div class="header-actions">
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>新增事件
            </el-button>
          </div>
        </div>
      </template>
      
      <el-card class="search-card">
        <el-form :inline="true" :model="search" class="search-form">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="事件编号">
                <el-input v-model="search.eventId" placeholder="请输入事件编号" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="事件类型">
                <el-select v-model="search.eventType" placeholder="请选择事件类型" clearable>
                  <el-option label="所有" value="" />
                  <el-option label="故障" value="故障" />
                  <el-option label="告警" value="告警" />
                  <el-option label="服务请求" value="服务请求" />
                  <el-option label="安全事件" value="安全事件" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="事件状态">
                <el-select v-model="search.status" placeholder="请选择事件状态" clearable>
                  <el-option label="所有" value="" />
                  <el-option label="待处理" value="待处理" />
                  <el-option label="处理中" value="处理中" />
                  <el-option label="已解决" value="已解决" />
                  <el-option label="已关闭" value="已关闭" />
                  <el-option label="已升级" value="已升级" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="报告人">
                <el-input v-model="search.reporter" placeholder="请输入报告人" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="优先级">
                <el-select v-model="search.priority" placeholder="请选择优先级" clearable>
                  <el-option label="所有" value="" />
                  <el-option label="紧急" value="紧急" />
                  <el-option label="高" value="高" />
                  <el-option label="中" value="中" />
                  <el-option label="低" value="低" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="发生时间">
                <el-date-picker
                  v-model="search.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" class="search-buttons-col">
              <el-form-item>
                <div class="button-container">
                  <el-button type="primary" @click="loadData">
                    <el-icon><Search /></el-icon>查询
                  </el-button>
                  <el-button @click="resetSearch">重置</el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      
      <el-table
        :data="tableData"
        border
        stripe
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column prop="eventId" label="事件编号" width="120" sortable />
        <el-table-column prop="title" label="事件标题" min-width="200" />
        <el-table-column prop="eventType" label="事件类型" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="scope">
            <el-tag :type="getPriorityType(scope.row.priority)">
              {{ scope.row.priority }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reporter" label="报告人" width="120" />
        <el-table-column prop="assignee" label="处理人" width="120" />
        <el-table-column prop="reportTime" label="报告时间" width="160" sortable />
        <el-table-column prop="resolveTime" label="解决时间" width="160" sortable />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="search.pageSize"
          :current-page="search.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { Plus, Search } from '@element-plus/icons-vue';

export default {
  name: 'EventManagement',
  components: {
    Plus,
    Search
  },
  data() {
    return {
      loading: false,
      search: {
        eventId: '',
        eventType: '',
        status: '',
        reporter: '',
        priority: '',
        dateRange: [],
        currentPage: 1,
        pageSize: 10
      },
      tableData: [
        // 示例数据，实际应从后端获取
        {
          id: 1,
          eventId: 'INC-2025-001',
          title: '核心交换机端口故障',
          eventType: '故障',
          status: '已解决',
          priority: '高',
          reporter: '张三',
          assignee: '李四',
          reportTime: '2025-05-15 08:30',
          resolveTime: '2025-05-15 10:45',
          description: '数据中心核心交换机G1/0/24端口无法正常工作，导致部分服务器无法访问。'
        },
        {
          id: 2,
          eventId: 'INC-2025-002',
          title: '应用服务器CPU使用率过高',
          eventType: '告警',
          status: '处理中',
          priority: '中',
          reporter: '系统监控',
          assignee: '王五',
          reportTime: '2025-05-16 14:20',
          resolveTime: null,
          description: 'APP01服务器CPU使用率持续超过90%，可能影响应用性能。'
        }
      ],
      total: 2
    };
  },
  methods: {
    loadData() {
      this.loading = true;
      // 模拟API调用
      setTimeout(() => {
        this.loading = false;
        // 实际项目中应该调用后端API获取数据
      }, 500);
    },
    resetSearch() {
      this.search = {
        eventId: '',
        eventType: '',
        status: '',
        reporter: '',
        priority: '',
        dateRange: [],
        currentPage: 1,
        pageSize: 10
      };
      this.loadData();
    },
    handleSizeChange(val) {
      this.search.pageSize = val;
      this.loadData();
    },
    handleCurrentChange(val) {
      this.search.currentPage = val;
      this.loadData();
    },
    handleAdd() {
      // 实现新增事件的逻辑
      this.$message.info('新增事件功能待实现');
    },
    handleView(row) {
      // 实现查看事件详情的逻辑
      this.$message.info(`查看事件: ${row.eventId}`);
    },
    handleEdit(row) {
      // 实现编辑事件的逻辑
      this.$message.info(`编辑事件: ${row.eventId}`);
    },
    handleDelete(row) {
      // 实现删除事件的逻辑
      this.$message.info(`删除事件: ${row.eventId}`);
    },
    getStatusType(status) {
      const statusMap = {
        '待处理': 'info',
        '处理中': 'warning',
        '已解决': 'success',
        '已关闭': 'info',
        '已升级': 'danger'
      };
      return statusMap[status] || 'info';
    },
    getPriorityType(priority) {
      const priorityMap = {
        '紧急': 'danger',
        '高': 'danger',
        '中': 'warning',
        '低': 'info'
      };
      return priorityMap[priority] || 'info';
    }
  },
  mounted() {
    this.loadData();
  }
};
</script>

<style scoped>
.event-management {
  padding: 20px;   
}

.main-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-card {
  margin-bottom: 20px;
  height: auto;
}

.search-form {
  width: 100%;
}

.button-container {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.search-buttons-col {
  display: flex;
  justify-content: flex-end;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
