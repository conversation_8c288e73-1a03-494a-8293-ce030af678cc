# 修复版本的nginx配置 - 解决405错误问题
# 主要修复：
# 1. 统一API代理配置
# 2. 修复负载均衡问题
# 3. 优化缓存策略
# 4. 添加调试头信息

upstream cmdb-web {
    server 127.0.0.1:3000 weight=1 max_fails=3 fail_timeout=30s;
    # 如果有多个后端服务器，可以添加更多server配置
    # server 127.0.0.1:3001 weight=1 max_fails=3 fail_timeout=30s backup;
}

# 主服务器配置（端口9000）
server {
    listen 9000;
    server_name *************;
    
    # 添加调试头信息
    add_header X-Primary-Server "true" always;
    add_header X-Server-Port "9000" always;

    # 根目录配置
    location / {
        root /opt/cmdb_v2.0/frontend/dist;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
        
        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API代理配置 - 直接代理到本地后端
    location /api/ {
        # 添加调试信息
        add_header X-API-Proxy "direct" always;
        
        proxy_pass http://127.0.0.1:3000/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Primary-Server "true";
        
        # 确保不缓存API响应
        proxy_cache_bypass 1;
        proxy_no_cache 1;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 确保请求方法不被改变
        proxy_method $request_method;
        proxy_pass_request_body on;
        proxy_pass_request_headers on;
    }

    # 文件上传代理
    location /upload/ {
        proxy_pass http://127.0.0.1:3000/upload/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 文件上传特殊配置
        client_max_body_size 100M;
        proxy_request_buffering off;
    }
}

# 备用服务器配置（端口80）
server {
    listen 80;
    server_name *************;
    
    # 添加调试头信息
    add_header X-Backup-Server "true" always;
    add_header X-Server-Port "80" always;

    # 根目录配置
    location / {
        root /opt/cmdb_v2.0/frontend/dist;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
        
        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API代理配置 - 使用负载均衡
    location /api/ {
        # 添加调试信息
        add_header X-API-Proxy "load-balanced" always;
        
        proxy_pass http://cmdb-web/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Backup-Server "true";
        
        # 确保不缓存API响应
        proxy_cache_bypass 1;
        proxy_no_cache 1;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 确保请求方法不被改变
        proxy_method $request_method;
        proxy_pass_request_body on;
        proxy_pass_request_headers on;
        
        # 负载均衡配置
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 30s;
    }

    # 文件上传代理
    location /upload/ {
        proxy_pass http://cmdb-web/upload/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 文件上传特殊配置
        client_max_body_size 100M;
        proxy_request_buffering off;
    }
}

# 全局配置
client_max_body_size 100M;
proxy_buffering off;
proxy_request_buffering off;
